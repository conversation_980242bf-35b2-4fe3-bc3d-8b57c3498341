﻿using AutoMapper;
using BlueTape.LS.DTOs.Loan;
using BlueTape.LS.DTOs.LoanReceivable;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Models.AccountApplicationsReport;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers;

[Route("[controller]")]
[ApiController]
public class QaController(
    ILoanService loanService,
    IMapper mapper,
    ILoanTapeReportService loanTapeReportService,
    IManualPaymentsReportService manualPaymentsReportService,
    IFeesReportService feesReportService,
    IRejectedAccountsApplicationsReportsService rejectedAccountsApplicationsReportsService,
    IRejectedInvoiceDrawsReportsService rejectedInvoiceDrawsReportsService,
    ILoanDownPaymentsService loanDownPaymentService)
    : ControllerBase
{
    ///
    /// <summary>
    /// Replace LoanReceivables, especially for QA
    /// </summary>
    /// <param name="id"></param>
    /// <param name="loanReceivablesModel"></param>
    /// <remarks>
    /// Sample request:
    ///
    ///     POST /Qa/Loans/{id}
    ///     "loanReceivables":
    ///     {
    ///         [
    ///             {
    ///                 "expectedDate": "2023-06-07",
    ///                 "actualDate": "2023-06-07",
    ///                 "expectedAmount": 100,
    ///                 "paidAmount": 0,
    ///                 "adjustDate": "2023-06-07",
    ///                 "adjustAmount": 0,
    ///                 "scheduleStatus": "Current",
    ///                 "status": "Pending",
    ///                 "type": "Installment",
    ///                 "loanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    ///                 "createdBy": "string",
    ///                 "updatedBy": "string"
    ///             }
    ///         ]
    ///     }
    /// </remarks>
    /// <returns></returns>
    [HttpPost(EndpointConstants.LoansById)]
    public async Task<LoanDto> ReplaceLoanReceivables([FromRoute] Guid id,
        [FromBody] CreateLoanReceivablesDto loanReceivablesModel, CancellationToken ct)
    {
        var loan = await loanService.ReplaceLoanReceivables(
            mapper.Map<IEnumerable<LoanReceivable>>(loanReceivablesModel.LoanReceivables), id, ct);
        return mapper.Map<LoanDto>(loan);
    }

    [HttpPost("draw-fees")]
    public Task TestDrawFeesReport(CancellationToken ct)
    {
        return feesReportService.GenerateReport(ct);
    }

    [HttpPost("manual-payments")]
    public Task TestManualPaymentsReport(CancellationToken ct)
    {
        return manualPaymentsReportService.GenerateReport(ct);
    }

    [HttpPost("loan-tape")]
    public Task TestLoanTape(CancellationToken ct)
    {
        return loanTapeReportService.Generate(ct);
    }

    [HttpPost("rejected-invoice-draws")]
    public Task TestRejectedApplicationsReport(CancellationToken ct)
    {
        return rejectedInvoiceDrawsReportsService.GenerateReport(ct);
    }

    [HttpPost("rejected-accounts-applications")]
    public Task<CreateRejectedAccountsApplicationsReportModel> TestRejectedAccountsApplications(CancellationToken ct)
    {
        return rejectedAccountsApplicationsReportsService.GenerateReport(ct);
    }

    [HttpPost("check-down-payment-expiration")]
    public Task CheckDownPaymentExpiration(CancellationToken ct)
    {
        return loanDownPaymentService.CheckDownPaymentExpiration(ct);
    }
}