﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	<ItemGroup>
		<PackageReference Include="BlueTape.CompanyClient" Version="1.0.46" />
		<PackageReference Include="BlueTape.CompanyService" Version="1.2.40" />
		<PackageReference Include="BlueTape.CompanyService.Common" Version="1.1.18" />
		<PackageReference Include="BlueTape.InvoiceService" Version="1.0.40" />
		<PackageReference Include="BlueTape.InvoiceService.Common" Version="1.1.3" />
		<PackageReference Include="BlueTape.OBS" Version="1.6.71" />
		<PackageReference Include="BlueTape.InvoiceClient" Version="1.0.21" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\BlueTape.OBS.Client\BlueTape.OBS.Client.csproj" />
	</ItemGroup>
</Project>
