﻿using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Entities.Filters;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Utilities.Models;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace BlueTape.Services.LMS.DataAccess.Abstractions.Repositories
{
    public interface ILoanRepository : IGenericRepository<LoanEntity>
    {
        Task<IEnumerable<LoanRelationsEntity>> AddLoanRelations(IEnumerable<LoanRelationsEntity> loanRelations,
            CancellationToken ct);
        Task ChangeLoanStatus(Guid id, LoanStatus loanStatus, CancellationToken ct);
        Task<IEnumerable<LoanEntity>> UpdateRangeWithPayments(IEnumerable<LoanEntity> entities, CancellationToken ct);
        Task<IEnumerable<LoanEntity>> UpdateRangeSyncTime(IEnumerable<LoanEntity> entities, CancellationToken ct);
        Task<LoanEntity?> UpdateWithPayments(LoanEntity entity, CancellationToken ct, ChangeLogEntity? changeLog = null);
        Task<LoanEntity?> GetByIdWithTracking(Guid id, CancellationToken ct);
        Task<IEnumerable<LoanEntity>?> GetLoansToSync(CancellationToken ct);
        Task<IEnumerable<LoanEntity>?> GetPaid(DateOnly fromDate, DateOnly toDate, CancellationToken ct);
        Task<IEnumerable<LoanEntity>?> GetOverdue(DateOnly fromDate, DateOnly toDate, CancellationToken ct);
        Task<IEnumerable<LoanEntity>?> GetUpcomingAndOverdueLoans(DateOnly date, CancellationToken ct);
        Task<IEnumerable<LoanEntity>?> GetByIds(IEnumerable<Guid> ids, CancellationToken ct);
        Task<List<string?>> GetAllCompanyIdsForLoansWithoutCredit(CancellationToken ct);
        Task<List<LoanEntity>> GetLoansWithoutParameters();

        Task<IReadOnlyCollection<LoanEntity>> GetLoansWithDetailedInfo(Expression<Func<LoanEntity, bool>> predicate,
            CancellationToken ct, int offset = 0, int count = int.MaxValue);
        Task<IEnumerable<LoanEntity>?> GetByEinHashAndStatus(LoansByEinHashAndStatusFilter filter, CancellationToken ct);
        Task HardDeleteRange(IEnumerable<LoanEntity> loansToDelete, CancellationToken ct);
        Task<List<string?>> GetAllCompanyIds(CancellationToken ct);
        Task<LoanEntity?> GetByPayableId(string payableId, CancellationToken ct);
        Task<LoanEntity?> GetByDrawApprovalId(string drawApprovalId, CancellationToken ct);
        Task<IEnumerable<LoanEntity>?> GetByDownPaymentStatuses(string[] downPaymentStatuses, CancellationToken ct);
        Task<IEnumerable<LoanEntity>?> GetByDownPaymentExpireDate(DateOnly fromDate, DateOnly toDate, CancellationToken ct);
        Task<IEnumerable<LoanEntity>?> GetByProduct(ProductType product, CancellationToken ct);
        Task<PaginatedResponse<LoanEntity>> GetByQuery(PaginatedLoanQuery queryParams, CancellationToken ct);
        Task UpdatePaymentProcessTemplate(Guid loanId, Guid paymentProcessTemplateId, CancellationToken ct);
        Task<IEnumerable<LoanEntity>> GetByDrawApprovalIds(string[] drawApprovalIds, CancellationToken ct);
        Task<List<Guid>> GetLoanIdsByExpression(Expression<Func<LoanEntity, bool>> predicate, CancellationToken ct);
        Task<IEnumerable<LoanRelationsEntity>> GetLoanRelations(Expression<Func<LoanRelationsEntity, bool>> predicate, CancellationToken ct);
    }
}
