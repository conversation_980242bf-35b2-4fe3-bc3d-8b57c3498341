﻿// <auto-generated />
using System;
using BlueTape.Services.LMS.DataAccess.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace BlueTape.Services.LMS.DataAccess.Migrations
{
    [DbContext(typeof(DatabaseContext))]
    [Migration("20250617124320_RemoveAuditTables")]
    partial class RemoveAuditTables
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.AuthorizationPeriodEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CanceledAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CanceledBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("CreditHoldAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("CreditId")
                        .HasColumnType("uuid");

                    b.Property<string>("DrawApprovalId")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LoanId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreditId");

                    b.HasIndex("LoanId");

                    b.ToTable("AuthorizationPeriods");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.BasisPointEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("BasisPointValue")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly>("ValidFrom")
                        .HasColumnType("date");

                    b.HasKey("Id");

                    b.ToTable("BasisPoints");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.ChangeLogEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ChangeAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EntityName")
                        .HasColumnType("text");

                    b.Property<Guid>("LoanId")
                        .HasColumnType("uuid");

                    b.Property<string>("NewValue")
                        .HasColumnType("text");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("OldValue")
                        .HasColumnType("text");

                    b.Property<string>("PrimaryKeyValue")
                        .HasColumnType("text");

                    b.Property<string>("PropertyName")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ChangeLogs");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.CreditEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateOnly?>("CloseDate")
                        .HasColumnType("date");

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreditApplicationId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("CreditLimit")
                        .HasColumnType("numeric");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ManualNote")
                        .HasColumnType("text");

                    b.Property<int?>("ManualStatus")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ManualStatusAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ManualStatusBy")
                        .HasColumnType("text");

                    b.Property<string>("MerchantId")
                        .HasColumnType("text");

                    b.Property<int>("PastDueDays")
                        .HasColumnType("integer");

                    b.Property<int>("Product")
                        .HasColumnType("integer");

                    b.Property<string>("ProjectId")
                        .HasColumnType("text");

                    b.Property<int?>("PurchaseType")
                        .HasColumnType("integer");

                    b.Property<double>("RevenueFallPercentage")
                        .HasColumnType("double precision");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("StatusEvent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Credits");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.CreditStatusHistoryEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CreditId")
                        .HasColumnType("uuid");

                    b.Property<string>("NewManualNote")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("NewManualStatus")
                        .HasColumnType("integer");

                    b.Property<int?>("NewStatus")
                        .HasColumnType("integer");

                    b.Property<string>("NewStatusEvent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreditId");

                    b.ToTable("CreditStatusHistories");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.DateProviderEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("CustomDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("DateProviders");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LatePaymentFeeDetailsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly>("FeeEndDate")
                        .HasColumnType("date");

                    b.Property<DateOnly>("FeeStartDate")
                        .HasColumnType("date");

                    b.Property<decimal>("LateAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("LoanReceivableId")
                        .HasColumnType("uuid");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("LoanReceivableId")
                        .IsUnique();

                    b.ToTable("LatePaymentFeeDetails");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LatePaymentFeeEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("ExpectedAmount")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("numeric");

                    b.Property<DateOnly?>("PaidDate")
                        .HasColumnType("date");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("LatePaymentFees");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ActiveLoanTemplateId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("AutoCollectionPausedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AutoCollectionPausedBy")
                        .HasColumnType("text");

                    b.Property<DateOnly?>("CloseDate")
                        .HasColumnType("date");

                    b.Property<string>("CompanyId")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreditId")
                        .HasColumnType("uuid");

                    b.Property<DateOnly?>("DefaultedDate")
                        .HasColumnType("date");

                    b.Property<int>("DownPaymentStatus")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("DownPaymentStatusAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DrawApprovalId")
                        .HasColumnType("text");

                    b.Property<string>("EinHash")
                        .HasColumnType("text");

                    b.Property<decimal>("Fee")
                        .HasColumnType("numeric");

                    b.Property<int>("FundingSource")
                        .HasColumnType("integer");

                    b.Property<bool>("IsAutoCollectionPaused")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsOverdue")
                        .HasColumnType("boolean");

                    b.Property<DateOnly?>("LastPaymentDate")
                        .HasColumnType("date");

                    b.Property<DateTime?>("LastSyncDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LoanOrigin")
                        .HasColumnType("integer");

                    b.Property<string>("MerchantId")
                        .HasColumnType("text");

                    b.Property<string>("MerchantName")
                        .HasColumnType("text");

                    b.Property<Guid?>("PaymentProcessTemplateId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProjectId")
                        .HasColumnType("text");

                    b.Property<decimal>("RefundAmount")
                        .HasColumnType("numeric");

                    b.Property<bool>("SkipLateFeeGeneration")
                        .HasColumnType("boolean");

                    b.Property<bool>("SkipPenaltyGeneration")
                        .HasColumnType("boolean");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ActiveLoanTemplateId");

                    b.HasIndex("CreditId");

                    b.HasIndex("PaymentProcessTemplateId");

                    b.ToTable("Loans");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanParametersEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ChangeType")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<decimal>("DownPaymentAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("DownPaymentExpireAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("DownPaymentPercentage")
                        .HasColumnType("numeric");

                    b.Property<int?>("EarlyPayPeriod")
                        .HasColumnType("integer");

                    b.Property<int>("EarlyPayPeriodCode")
                        .HasColumnType("integer");

                    b.Property<DateOnly?>("EarlyPaymentDeadlineDate")
                        .HasColumnType("date");

                    b.Property<DateOnly>("FirstPaymentDate")
                        .HasColumnType("date");

                    b.Property<int>("GracePeriodInDays")
                        .HasColumnType("integer");

                    b.Property<int>("InstallmentsNumber")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("LateFeeCollectionDelayInDays")
                        .HasColumnType("integer");

                    b.Property<decimal>("LateFeePercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal>("LoanFeePercentage")
                        .HasColumnType("numeric");

                    b.Property<Guid>("LoanId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LoanTemplateId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("MinimumLateFeeAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<int>("PaymentDelayCode")
                        .HasColumnType("integer");

                    b.Property<int?>("PaymentDelayInDays")
                        .HasColumnType("integer");

                    b.Property<int>("PaymentIntervalInDays")
                        .HasColumnType("integer");

                    b.Property<int>("PenaltyInterestTriggerRule")
                        .HasColumnType("integer");

                    b.Property<int>("TotalDurationInDays")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("LoanId");

                    b.HasIndex("LoanTemplateId");

                    b.ToTable("LoanParameters");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanPayablesEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("Amount")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("InvoiceNumber")
                        .HasColumnType("text");

                    b.Property<Guid>("LoanId")
                        .HasColumnType("uuid");

                    b.Property<string>("PayableId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("LoanId");

                    b.ToTable("LoanPayables");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanReceivableEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AdjustAmount")
                        .HasColumnType("numeric");

                    b.Property<DateOnly?>("AdjustDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<decimal>("ExpectedAmount")
                        .HasColumnType("numeric");

                    b.Property<DateOnly>("ExpectedDate")
                        .HasColumnType("date");

                    b.Property<Guid>("LoanId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("numeric");

                    b.Property<DateOnly?>("PaidDate")
                        .HasColumnType("date");

                    b.Property<decimal>("ProcessingAmount")
                        .HasColumnType("numeric");

                    b.Property<DateOnly?>("ProcessingDate")
                        .HasColumnType("date");

                    b.Property<int>("ScheduleStatus")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("LoanId");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.ToTable("LoanReceivables");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanReceivablesPaymentsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsCorrection")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("LoanReceivableId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PaymentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("LoanReceivableId");

                    b.HasIndex("PaymentId");

                    b.ToTable("LoanReceivablesPayments");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanRelationsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ChildLoanId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("ParentLoanId")
                        .HasColumnType("uuid");

                    b.Property<int>("RelationType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ChildLoanId");

                    b.HasIndex("ParentLoanId");

                    b.HasIndex("RelationType");

                    b.ToTable("LoanRelations");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanTemplateEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("EarlyPayPeriod")
                        .HasColumnType("integer");

                    b.Property<int>("EarlyPayPeriodCode")
                        .HasColumnType("integer");

                    b.Property<int>("GracePeriodInDays")
                        .HasColumnType("integer");

                    b.Property<int>("InstallmentsNumber")
                        .HasColumnType("integer");

                    b.Property<int>("LateFeeCollectionDelayInDays")
                        .HasColumnType("integer");

                    b.Property<decimal>("LateFeePercentage")
                        .HasColumnType("numeric");

                    b.Property<string>("LegacyId")
                        .HasColumnType("text");

                    b.Property<decimal>("LoanFeePercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal>("MinimumLateFeeAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("PaymentDelayCode")
                        .HasColumnType("integer");

                    b.Property<int?>("PaymentDelayInDays")
                        .HasColumnType("integer");

                    b.Property<int>("PaymentIntervalInDays")
                        .HasColumnType("integer");

                    b.Property<int>("PenaltyInterestTriggerRule")
                        .HasColumnType("integer");

                    b.Property<int>("Product")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("TemplateType")
                        .HasColumnType("integer");

                    b.Property<int>("TotalDurationInDays")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("LoanTemplates");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.PaymentEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<Guid>("LoanId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("SubType")
                        .HasColumnType("integer");

                    b.Property<string>("TransactionNumber")
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("LoanId");

                    b.ToTable("Payments");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.PaymentProcessTemplateEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CollectionTemplateCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DisbursementTemplateCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FinalPaymentTemplateCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<int>("Product")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("PaymentProcessTemplates");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.PaymentProcessTemplateHistoryEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly?>("EndDate")
                        .HasColumnType("date");

                    b.Property<Guid>("LoanId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PaymentProcessTemplateId")
                        .HasColumnType("uuid");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("LoanId");

                    b.HasIndex("PaymentProcessTemplateId");

                    b.ToTable("PaymentProcessTemplateHistory");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.PenaltyExclusionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerId")
                        .HasColumnType("text");

                    b.Property<DateOnly?>("DateFrom")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("DateTo")
                        .HasColumnType("date");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LoanId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ReceivableId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("PenaltyExclusions");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.PenaltyInterestDetailsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("BasisPoint")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("LoanReceivableId")
                        .HasColumnType("uuid");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<decimal>("OutstandingPrincipalAmount")
                        .HasColumnType("numeric");

                    b.Property<DateOnly>("PenaltyEndDate")
                        .HasColumnType("date");

                    b.Property<DateOnly>("PenaltyStartDate")
                        .HasColumnType("date");

                    b.Property<DateOnly>("SOFRDay")
                        .HasColumnType("date");

                    b.Property<int>("SOFRRate")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("LoanReceivableId")
                        .IsUnique();

                    b.ToTable("PenaltyInterestDetails");
                });

            modelBuilder.Entity("BlueTape.Services.Reporting.Domain.Entities.LoanReportsHistoryEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Report")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("LoanReportsHistory");
                });

            modelBuilder.Entity("BlueTape.Services.Reporting.Domain.Entities.LoanReportsHistoryItemEntity", b =>
                {
                    b.Property<Guid>("LoanId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("LoanReportsHistoryId")
                        .HasColumnType("uuid");

                    b.HasKey("LoanId", "LoanReportsHistoryId");

                    b.HasIndex("LoanReportsHistoryId");

                    b.ToTable("LoanReportsHistoryItems");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.AuthorizationPeriodEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.CreditEntity", "Credit")
                        .WithMany("CreditHolds")
                        .HasForeignKey("CreditId");

                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanEntity", "Loan")
                        .WithMany("AuthorizationPeriods")
                        .HasForeignKey("LoanId");

                    b.Navigation("Credit");

                    b.Navigation("Loan");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.CreditStatusHistoryEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.CreditEntity", "Credit")
                        .WithMany()
                        .HasForeignKey("CreditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Credit");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LatePaymentFeeDetailsEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanReceivableEntity", "LoanReceivable")
                        .WithOne("LatePaymentFeeDetails")
                        .HasForeignKey("BlueTape.Services.LMS.Domain.Entities.LatePaymentFeeDetailsEntity", "LoanReceivableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LoanReceivable");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanTemplateEntity", "ActiveLoanTemplate")
                        .WithMany()
                        .HasForeignKey("ActiveLoanTemplateId");

                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.CreditEntity", "Credit")
                        .WithMany("Loans")
                        .HasForeignKey("CreditId");

                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.PaymentProcessTemplateEntity", "PaymentProcessTemplate")
                        .WithMany()
                        .HasForeignKey("PaymentProcessTemplateId");

                    b.Navigation("ActiveLoanTemplate");

                    b.Navigation("Credit");

                    b.Navigation("PaymentProcessTemplate");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanParametersEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanEntity", "Loan")
                        .WithMany("LoanParameters")
                        .HasForeignKey("LoanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanTemplateEntity", "LoanTemplate")
                        .WithMany()
                        .HasForeignKey("LoanTemplateId");

                    b.Navigation("Loan");

                    b.Navigation("LoanTemplate");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanPayablesEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanEntity", "Loan")
                        .WithMany("LoanPayables")
                        .HasForeignKey("LoanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Loan");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanReceivableEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanEntity", "Loan")
                        .WithMany("LoanReceivables")
                        .HasForeignKey("LoanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Loan");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanReceivablesPaymentsEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanReceivableEntity", "LoanReceivable")
                        .WithMany("LoanReceivablesPayments")
                        .HasForeignKey("LoanReceivableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.PaymentEntity", "Payment")
                        .WithMany("LoanReceivablesPayments")
                        .HasForeignKey("PaymentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LoanReceivable");

                    b.Navigation("Payment");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanRelationsEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanEntity", null)
                        .WithMany()
                        .HasForeignKey("ChildLoanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanEntity", null)
                        .WithMany()
                        .HasForeignKey("ParentLoanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.PaymentEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanEntity", "Loan")
                        .WithMany("Payments")
                        .HasForeignKey("LoanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Loan");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.PaymentProcessTemplateHistoryEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanEntity", "Loan")
                        .WithMany()
                        .HasForeignKey("LoanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.PaymentProcessTemplateEntity", "PaymentProcessTemplate")
                        .WithMany()
                        .HasForeignKey("PaymentProcessTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Loan");

                    b.Navigation("PaymentProcessTemplate");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.PenaltyInterestDetailsEntity", b =>
                {
                    b.HasOne("BlueTape.Services.LMS.Domain.Entities.LoanReceivableEntity", "LoanReceivable")
                        .WithOne("PenaltyInterestDetails")
                        .HasForeignKey("BlueTape.Services.LMS.Domain.Entities.PenaltyInterestDetailsEntity", "LoanReceivableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LoanReceivable");
                });

            modelBuilder.Entity("BlueTape.Services.Reporting.Domain.Entities.LoanReportsHistoryItemEntity", b =>
                {
                    b.HasOne("BlueTape.Services.Reporting.Domain.Entities.LoanReportsHistoryEntity", "LoanReportsHistory")
                        .WithMany("LoanReportsHistoryItems")
                        .HasForeignKey("LoanReportsHistoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LoanReportsHistory");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.CreditEntity", b =>
                {
                    b.Navigation("CreditHolds");

                    b.Navigation("Loans");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanEntity", b =>
                {
                    b.Navigation("AuthorizationPeriods");

                    b.Navigation("LoanParameters");

                    b.Navigation("LoanPayables");

                    b.Navigation("LoanReceivables");

                    b.Navigation("Payments");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.LoanReceivableEntity", b =>
                {
                    b.Navigation("LatePaymentFeeDetails");

                    b.Navigation("LoanReceivablesPayments");

                    b.Navigation("PenaltyInterestDetails");
                });

            modelBuilder.Entity("BlueTape.Services.LMS.Domain.Entities.PaymentEntity", b =>
                {
                    b.Navigation("LoanReceivablesPayments");
                });

            modelBuilder.Entity("BlueTape.Services.Reporting.Domain.Entities.LoanReportsHistoryEntity", b =>
                {
                    b.Navigation("LoanReportsHistoryItems");
                });
#pragma warning restore 612, 618
        }
    }
}
