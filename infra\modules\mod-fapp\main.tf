# ## Create Environment Specific Function App
# ## Base image for Azure Funtions used:
# ## Ref.: https://hub.docker.com/_/microsoft-azure-functions-dotnet-isolated
resource "azurerm_linux_function_app" "loan" {
  name                       = "func-${var.application_name.slug}-${var.environment}-1"
  location                   = var.location
  resource_group_name        = var.resource_group_name
  service_plan_id            = var.service_plan_core_id
  https_only                 = true
  storage_account_name       = var.storage_account_name
  storage_account_access_key = var.storage_account_access_key
  enabled                    = true

  app_settings = {
    "FUNCTIONS_WORKER_RUNTIME"               = var.functions_worker_runtime
    "ASPNETCORE_ENVIRONMENT"                 = var.environment
    "KEYVAULT_URI"                           = var.key_vault_uri
    "AZURE_CLIENT_SECRET"                    = var.client_secret
    "AZURE_TENANT_ID"                        = var.tenant_id
    "AZURE_CLIENT_ID"                        = var.client_id
    "FUNCTIONS_EXTENSION_VERSION"            = var.function_extension_version
    "APPINSIGHTS_INSTRUMENTATIONKEY"         = var.app_insights_instrumentation_key
    "AzureWebJobsStorage"                    = var.storage_account_primary_connection_string
    "AWS_ACCESS_KEY_ID"                      = var.aws_access_key_id
    "AWS_SECRET_ACCESS_KEY"                  = var.aws_secret_access_key
    "AWS_DEFAULT_REGION"                     = var.aws_default_region
    "creditStatusQueueName"                 = var.credit_status_queue_name
    "creditStatusQueueConnection"           = var.credit_status_queue_connection
    "AWS_REGION"                             = var.aws_default_region
    "LP_AWS_ACCOUNT"                         = var.lp_aws_account
    "VNET_TEST_SETTING"                      = "testing-vnet-integration-fix"
  }

  site_config {
    app_scale_limit                         = 2
    always_on                               = var.environment != "dev"
    ftps_state                              = "FtpsOnly"
    application_insights_key                = var.app_insights_instrumentation_key
    application_insights_connection_string  = var.app_insights_connection_string

    application_stack {
      dotnet_version                        = var.dotnet_version
      use_dotnet_isolated_runtime           = true
    }
  }

  key_vault_reference_identity_id = var.user_assigned_identity_core_id

  identity {
      type = "UserAssigned"
      identity_ids = [var.user_assigned_identity_core_id]
  }

  tags = {
    Environment = title(var.environment)
    Application = "Core"
    Type        = "Function App"
    ManagedBy   = "Terraform"
  }

  lifecycle {
    ignore_changes = [
      tags["CreatedOn"],
    ]
  }
}

resource "azurerm_app_service_virtual_network_swift_connection" "connection" {
  count          = var.environment != "dev" ? 1: 0
  app_service_id = azurerm_linux_function_app.loan.id
  subnet_id      = var.virtual_network_subnet_id

  depends_on = [
    azurerm_linux_function_app.loan
  ]

  lifecycle {
    create_before_destroy = true
  }
}