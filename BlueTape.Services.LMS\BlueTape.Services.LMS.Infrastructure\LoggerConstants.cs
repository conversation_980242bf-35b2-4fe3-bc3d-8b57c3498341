﻿namespace BlueTape.Services.LMS.Infrastructure;

public static class LoggerConstants
{
    public const string ProjectValue = "LMS";
    public const string ProjectName = "ProjectName";

    public const string OverDueFunctionName = "OverDueDetectorFunction";
    public const string PenaltyDetectorFunctionName = "PenaltyDetectorFunction";
    public const string CreditDetectorFunctionName = "CreditDetectorFunction";
    public const string LoanTapeReportFunctionName = "LoanTapeReportFunction";


    public const string EnvironmentName = "EnvironmentName";
    public const string ContentRootPath = "ContentRootPath";
    public const string LogzioToken = "LOGZIO_TOKEN";
    public const string LogzioParsingType = "http-bulk";
    public const string LogzioSubDomain = "listener";
    public const int LogzioPort = 8070;
    public const string BlueTapeCorrelationId = "BlueTapeCorrelationId";
    public const string Path = "Path";
    public const string Method = "Method";
    public const string AppInsightsConnection = "APP-INSIGHTS-CONNECTION";
}