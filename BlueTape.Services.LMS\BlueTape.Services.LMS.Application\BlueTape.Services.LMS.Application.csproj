﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="BlueTape.CompanyService" Version="1.2.40" />
    <PackageReference Include="BlueTape.DraftsMapper" Version="1.0.1" />
    <PackageReference Include="bluetape.ls" Version="1.1.78" />
    <PackageReference Include="bluetape.ls.domain" Version="1.1.36" />
    <PackageReference Include="BlueTape.Notification.Sender" Version="1.0.4" />
    <PackageReference Include="BlueTape.OBS" Version="1.6.71" />
    <PackageReference Include="BlueTape.Utilities" Version="1.4.6" />
    <PackageReference Include="BlueTape.CompanyService.Common" Version="1.1.18" />
    <PackageReference Include="BlueTape.InvoiceService" Version="1.0.40" />
    <PackageReference Include="BlueTape.LMS" Version="1.0.2" />
    <PackageReference Include="BlueTape.InvoiceService.Common" Version="1.1.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlueTape.DataAccess.Mongo\BlueTape.DataAccess.Mongo.csproj" />
    <ProjectReference Include="..\BlueTape.OBS.Client\BlueTape.OBS.Client.csproj" />
    <ProjectReference Include="..\BlueTape.Services.ARS.Models\BlueTape.Services.ARS.Models.csproj" />
    <ProjectReference Include="..\BlueTape.Services.DataAccess.External\BlueTape.Services.DataAccess.External.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.DataAccess.Company\BlueTape.Services.LMS.DataAccess.Company.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.DataAccess.SOFR\BlueTape.Services.LMS.DataAccess.SOFR.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.UnitOfWork\BlueTape.Services.LMS.UnitOfWork.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.DataAccess\BlueTape.Services.LMS.DataAccess.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.Domain\BlueTape.Services.LMS.Domain.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.Infrastructure\BlueTape.Services.LMS.Infrastructure.csproj" />
    <ProjectReference Include="..\BlueTape.Services.Reporting\BlueTape.Services.Reporting.csproj" />
  </ItemGroup>

</Project>
