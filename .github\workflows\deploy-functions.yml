name: Functions Deploy

on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: Select the environment
        required: true

env:
  NUGET_PACKAGES_DIRECTORY: ".nuget"
  BRANCH: "${{ github.ref }}"
  AZURE_FUNCTION_APP_NAME: "func-core-${{ vars.STAGE }}"
  PACKAGE_REGISTRY_USERNAME: ${{ secrets.PACKAGE_REGISTRY_USERNAME }}
  PACKAGE_REGISTRY_PASSWORD: ${{ secrets.PACKAGE_REGISTRY_PASSWORD }}

jobs:
  deploy:
    environment: ${{ github.event.inputs.environment }}
    name: "Deploying ${{ github.event.inputs.environment }} environment"
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 0

      - name: Log in to Azure
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - run: sudo apt-get update
      - run: sudo apt-get -y install zip
      - run: dotnet tool restore
      
      - run: dotnet publish --configuration "Release" BlueTape.Services.LMS/Functions/BlueTape.Functions.LMS.OverDueDetector/BlueTape.Functions.LMS.OverDueDetector.csproj --runtime "linux-x64" --no-self-contained -o functions
      - run: (cd functions && zip -r ../functions-${{ github.run_id }}.zip .)

      - name: 'Deploy Functions'
        uses: Azure/functions-action@v1.4.7
        with:
         app-name: "func-lms-dev"
         package: "functions-${{ github.run_id }}.zip"
         publish-profile: ${{ secrets.AZURE_FUNCTIONAPP_PUBLISH_PROFILE }}
