variable "environment" {
  type    = string
}

variable "application_name" {
  type = map(any)
}

variable "key_vault_id" {
  type    = string
}

variable "resource_group_name" {
  type    = string
}

variable "resource_group_location" {
  type    = string
}

variable "credit_status_queue_name" {
  default = "creditStatusQueueName"
}

variable "credit_status_queue_connection" {
  default = "creditStatusQueueConnection"
}