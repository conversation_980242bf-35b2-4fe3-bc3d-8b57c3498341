﻿using AutoMapper;
using BlueTape.OBS.Enums;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.PaymentServices;
using BlueTape.Services.LMS.Application.Constants;
using BlueTape.Services.LMS.Application.Extensions;
using BlueTape.Services.LMS.Application.Infrastructure.ChangeLoanStatusStrategy.Abstractions;
using BlueTape.Services.LMS.Application.Models.ChangeLog;
using BlueTape.Services.LMS.Application.Models.Filters;
using BlueTape.Services.LMS.Application.Models.LoanPayables;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Application.Services.Base;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using BlueTape.Utilities.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TinyHelpers.Extensions;
using static BlueTape.Services.LMS.Application.Utils.JsonSerializationSettings;
using RepositoryLoansByEinHashAndStatusFilter = BlueTape.Services.LMS.Domain.Entities.Filters.LoansByEinHashAndStatusFilter;

namespace BlueTape.Services.LMS.Application.Services.LoanServices
{
    public class LoanService : GenericService<Loan, LoanEntity>, ILoanService
    {
        private readonly ILoanRepository _loanRepository;
        private readonly IPaymentProcessTemplateHistoryRepository _paymentProcessTemplateHistoryRepository;
        private readonly IPaymentProcessTemplateRepository _paymentProcessTemplateRepository;
        private readonly IEnumerable<IChangeLoanStatusStrategy> _changeLoanStatusStrategies;
        private readonly ILoanReceivableService _loanReceivableService;
        private readonly IProcessingAmountService _processingAmountService;
        private readonly ILoanTemplateRepository _loanTemplateRepository;
        private readonly IChangeLogService _changeLogService;
        private readonly ILogger<LoanService> _logger;
        private readonly ICreateLoanService _createLoanService;
        private readonly ILoanDetailsService _loanDetailsService;

        public LoanService(
            ILoanRepository loanRepository,
            IPaymentProcessTemplateRepository paymentProcessTemplateRepository,
            IEnumerable<IChangeLoanStatusStrategy> changeLoanStatusStrategies,
            IMapper mapper,
            ILoanReceivableService loanReceivableService,
            ILoanTemplateRepository loanTemplateRepository,
            IProcessingAmountService processingAmountService,
            IChangeLogService changeLogService,
            ILogger<LoanService> logger,
            ICreateLoanService createLoanService,
            ILoanDetailsService loanDetailsService,
            IPaymentProcessTemplateHistoryRepository paymentProcessTemplateHistoryRepository) : base(loanRepository, mapper)
        {
            _loanRepository = loanRepository;
            _paymentProcessTemplateRepository = paymentProcessTemplateRepository;
            _changeLoanStatusStrategies = changeLoanStatusStrategies;
            _loanReceivableService = loanReceivableService;
            _loanTemplateRepository = loanTemplateRepository;
            _changeLogService = changeLogService;
            _logger = logger;
            _createLoanService = createLoanService;
            _loanDetailsService = loanDetailsService;
            _paymentProcessTemplateHistoryRepository = paymentProcessTemplateHistoryRepository;
            _processingAmountService = processingAmountService;
        }

        public async Task<IEnumerable<Loan>> Get(bool? detailed, CancellationToken ct)
        {
            var loanEntities = (await _loanRepository.GetByQuery(new()
            {
                PageNumber = 1,
                PageSize = 100,
            }, ct)).Result;

            loanEntities.ForEach(loanEntity =>
            {
                loanEntity.LoanReceivables = loanEntity.LoanReceivables.GetOrdered();
            });
            var loans = Mapper.Map<IEnumerable<Loan>>(loanEntities);
            CalculateLoanDetails(loans, detailed);
            return loans;
        }

        public async Task<Loan> GetById(Guid id, bool? detailed, CancellationToken ct)
        {
            var loanEntity = await _loanRepository.GetById(id, ct);

            if (loanEntity is null) throw new VariableNullException(ExceptionConstants.LoanNullMessage);

            loanEntity.LoanReceivables = loanEntity.LoanReceivables.GetOrdered();
            var loan = Mapper.Map<Loan>(loanEntity);
            CalculateLoanDetails(loan, detailed);
            return loan;
        }

        public async Task ChangeLoanStatus(ChangeLoanStatusModel changeLoanStatusModel, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Start changing status for loan id:{id}", changeLoanStatusModel.Id);

            await _changeLoanStatusStrategies.OrderBy(strategy => strategy.Order)
                .First(x => x.IsApplicable(changeLoanStatusModel.Status)).ChangeStatus(changeLoanStatusModel, cancellationToken);

            _logger.LogInformation("End changing status for loan id:{id}", changeLoanStatusModel.Id);
        }

        public async Task ChangeLoanAutoCollection(Guid id, string userId, bool isAutoCollectionPaused, CancellationToken ct)
        {
            var loanEntity = await _loanRepository.GetById(id, ct);

            if (loanEntity is null) throw new VariableNullException(ExceptionConstants.LoanNullMessage);

            if (loanEntity.IsAutoCollectionPaused != isAutoCollectionPaused)
            {
                loanEntity.IsAutoCollectionPaused = isAutoCollectionPaused;
                loanEntity.AutoCollectionPausedBy = userId;
                loanEntity.AutoCollectionPausedAt =DateTime.UtcNow;
                await _loanRepository.Update(loanEntity, ct);
            }
        }

        public async Task<Loan> Create(CreateLoan createLoan, CancellationToken ct)
        {
            var loanTemplateId = createLoan.LoanTemplateId;
            var loanTemplate = await _loanTemplateRepository.GetById(loanTemplateId, ct);

            if (loanTemplate is not null && createLoan.LoanOrigin is null)
            {
                switch (loanTemplate.Product)
                {
                    case Domain.Enums.ProductType.LineOfCredit:
                        createLoan.LoanOrigin = LoanOrigin.Normal;
                        break;
                    case Domain.Enums.ProductType.InHouseCredit:
                        createLoan.LoanOrigin = LoanOrigin.Factoring;
                        break;
                    case Domain.Enums.ProductType.ARAdvance:
                        createLoan.LoanOrigin = LoanOrigin.Express;
                        break;
                }
            }

            return await _createLoanService.Create(createLoan, ct);
        }

        public async Task<IEnumerable<Loan>?> GetLoansToSync(bool? detailed, CancellationToken ct)
        {
            var loanEntities = await _loanRepository.GetLoansToSync(ct);

            if (loanEntities is not null)
            {
                loanEntities = await _loanRepository.UpdateRangeSyncTime(loanEntities, ct);
            }
            var loans = Mapper.Map<IEnumerable<Loan>>(loanEntities);
            CalculateLoanDetails(loans, detailed);
            return loans;
        }

        public async Task<IEnumerable<Loan>?> GetPaid(DateOnly fromDate, DateOnly toDate, bool? detailed, CancellationToken ct)
        {
            var loans = Mapper.Map<IEnumerable<Loan>>(await _loanRepository.GetPaid(fromDate, toDate, ct));
            CalculateLoanDetails(loans, detailed);

            return loans;
        }

        public async Task<IEnumerable<Loan>?> GetByProjectId(string projectId, bool? detailed, CancellationToken ct)
        {
            var loans = Mapper.Map<IEnumerable<Loan>?>(await _loanRepository.Get(x => x.ProjectId == projectId, ct));
            CalculateLoanDetails(loans, detailed);

            return loans;
        }


        public async Task<IEnumerable<Loan>?> GetByProduct(Domain.Enums.ProductType product, bool? detailed, CancellationToken ct)
        {
            var loans = Mapper.Map<IEnumerable<Loan>?>(await _loanRepository.GetByProduct(product, ct));
            CalculateLoanDetails(loans, detailed);

            return loans;
        }

        public async Task<IEnumerable<Loan>?> GetByPayableId(string payableId, bool? detailed, CancellationToken ct)
        {
            var loanEntity = await _loanRepository.GetByPayableId(payableId, ct);
            var loans = loanEntity != null
                          ? Mapper.Map<IEnumerable<Loan>>(new List<LoanEntity> { loanEntity })
                          : [];

            if (loans.Any())
                CalculateLoanDetails(loans, detailed);

            return loans;
        }

        public async Task ChangePaymentProcessTemplate(Guid loanId, Guid paymentTemplateId, CancellationToken ct)
        {
            var paymentTemplate = await _paymentProcessTemplateRepository.GetById(paymentTemplateId, ct);

            var loan = await _loanRepository.GetById(loanId, ct);

            if (paymentTemplate is null) throw new VariableNullException(ExceptionConstants.PaymentProcessTemplateNullMessage);

            if (loan is null) throw new VariableNullException(ExceptionConstants.LoanNullMessage);

            _logger.LogInformation("Start changing payment process template for loan id:{id}, paymentTemplateId:{paymentTemplateId}", loanId, paymentTemplateId);
            await _loanRepository.UpdatePaymentProcessTemplate(loanId, paymentTemplateId, ct);
            _logger.LogInformation("End changing payment process template for loan id:{id}, paymentTemplateId:{paymentTemplateId}", loanId, paymentTemplateId);

            var paymentProcessTemplateHistory = new PaymentProcessTemplateHistoryEntity()
            {
                LoanId = loanId,
                PaymentProcessTemplateId = paymentTemplateId,
                StartDate = DateTime.UtcNow.ToDateOnly(),
            };

            await _paymentProcessTemplateHistoryRepository.Add(paymentProcessTemplateHistory, ct);
        }

        public async Task<IEnumerable<Loan>?> GetByDownPaymentStatuses(string[] downPaymentStatuses, bool? detailed, CancellationToken ct)
        {
            var loanEntities = await _loanRepository.GetByDownPaymentStatuses(downPaymentStatuses, ct);
            var loans = Mapper.Map<IEnumerable<Loan>>(loanEntities);

            if (loans.Any())
                CalculateLoanDetails(loans, detailed);

            return loans;
        }

        public async Task<IEnumerable<Loan>?> GetByDownPaymentExpireDate(DateOnly fromDate, DateOnly toDate, bool? detailed, CancellationToken ct)
        {
            var loanEntities = await _loanRepository.GetByDownPaymentExpireDate(fromDate, toDate, ct);
            var loans = Mapper.Map<IEnumerable<Loan>>(loanEntities);

            if (loans.Any())
                CalculateLoanDetails(loans, detailed);

            return loans;
        }

        public async Task<IEnumerable<Loan>?> GetByDrawApprovalId(string drawApprovalId, bool? detailed, CancellationToken ct)
        {
            var loanEntity = await _loanRepository.GetByDrawApprovalId(drawApprovalId, ct);
            var loans = loanEntity != null
                ? Mapper.Map<IEnumerable<Loan>>(new List<LoanEntity> { loanEntity })
                : [];

            if (loans.Any())
                CalculateLoanDetails(loans, detailed);

            return loans;
        }

        public async Task<IEnumerable<Loan>?> GetByDrawApprovalIds(string[] drawApprovalIds, bool? detailed, CancellationToken ct)
        {
            var loanEntities = await _loanRepository.GetByDrawApprovalIds(drawApprovalIds, ct);
            var loans = Mapper.Map<IEnumerable<Loan>?>(loanEntities);
            CalculateLoanDetails(loans, detailed);
            return loans;
        }

        public async Task<IEnumerable<Loan>?> GetOverdue(DateOnly fromDate, DateOnly toDate, bool? detailed, CancellationToken ct)
        {
            var loans = Mapper.Map<IEnumerable<Loan>>(await _loanRepository.GetOverdue(fromDate, toDate, ct));
            CalculateLoanDetails(loans, detailed);
            return loans;
        }

        public async Task<IEnumerable<Loan>?> GetByIds(List<Guid> ids, bool? detailed, CancellationToken ct)
        {
            var loans = Mapper.Map<IEnumerable<Loan>?>(await _loanRepository.GetByIds(ids, ct));
            CalculateLoanDetails(loans, detailed);
            return loans;
        }

        public async Task<IEnumerable<Loan>?> GetUpcomingAndOverdueLoans(DateOnly date, bool? detailed, CancellationToken ct)
        {
            var loans = Mapper.Map<IEnumerable<Loan>>(await _loanRepository.GetUpcomingAndOverdueLoans(date, ct));
            CalculateLoanDetails(loans, detailed);
            return loans;
        }

        public async Task<Loan> UpdateLoanReceivablesForLoan(IEnumerable<LoanReceivable> loanReceivables, Guid loanId, string userId, CancellationToken ct, string? note = default)
        {
            _logger.LogInformation("Start updating loan LoanReceivables for loan id:{loanId}", loanId);

            var loan = await _loanRepository.GetByIdWithTracking(loanId, ct);
            if (loan is null) throw new VariableNullException(ExceptionConstants.LoanNullMessage);

            var currentLoanReceivables = Mapper.Map<List<LoanReceivable>>(loan.LoanReceivables);

            var recalculatedLoanReceivables = _loanReceivableService
                .UpdateLoanReceivablesForLoan(loan.LoanReceivables, Mapper.Map<List<LoanReceivableEntity>>(loanReceivables), loanId, ct);

            await _changeLogService
                .AddUpdatedLoanReceivablesLog(currentLoanReceivables,
                    Mapper.Map<List<LoanReceivable>>(recalculatedLoanReceivables), note!, userId, ct);

            loan.LoanReceivables = recalculatedLoanReceivables;

            loan.Fee = GetUpdatedLoanFee(loan.LoanReceivables);

            _logger.LogInformation("Finished updating loan id:{loanId} LoanReceivables. Loan: {loan}", loanId, JsonConvert.SerializeObject(loan, JsonConvertOptions));

            var updatedLoan = await _loanRepository.UpdateWithPayments(loan, ct);
            updatedLoan!.LoanReceivables = await _processingAmountService.CalculateProcessingAmount(loanId, ct);

            return Mapper.Map<Loan>(updatedLoan);
        }

        //Excluded from coverage as it's developed for internal use only
        [ExcludeFromCodeCoverage]
        public async Task<Loan> Refinance(RefinanceLoanModel refinanceLoans, string userId, CancellationToken ct)
        {
            var loanRelations = new List<LoanRelationsEntity>();
            var loans = await GetByIds(refinanceLoans.LoansToRefinance.Select(x => x.LoanId).ToList(), false, ct);

            if (loans.Count() != refinanceLoans.LoansToRefinance.Count()) throw new ValidationException("Some loans do not exist");
            var isCompanyIdsEqual = loans.Select(x => x.CompanyId).Distinct().Count() == 1;
            if (!isCompanyIdsEqual) throw new ValidationException("Loans have different company ids");
            var isMerchantIdsEqual = loans.Select(x => x.MerchantId).Distinct().Count() == 1;
            if (!isMerchantIdsEqual) throw new ValidationException("Loans have different merchant ids");
            var firstLoan = loans.First();

            var createPayablesModel = loans
                .SelectMany(x => x.LoanPayables)
                .Select(payable => new LoanPayablesCreateModel()
                {
                    Amount = payable.Amount,
                    InvoiceNumber = payable.InvoiceNumber ?? string.Empty,
                    PayableId = payable.PayableId,
                }).ToList();

            var createdLoan = await Create(new CreateLoan()
            {
                LoanTemplateId = refinanceLoans.NewLoanTemplateId,
                Amount = refinanceLoans.LoansToRefinance.Sum(x => x.Amount),
                CompanyId = firstLoan.CompanyId,
                EinHash = firstLoan.EinHash,
                RefinanceFeePercentage = refinanceLoans.RefinanceFeePercentage,
                FundingSource = Domain.Enums.FundingSource.Arcadia,
                LoanOrigin = LoanOrigin.Normal,
                MerchantId = firstLoan.MerchantId,
                MerchantName = firstLoan.MerchantName,
                CompanyName = firstLoan.CompanyName,
                LoanPayables = createPayablesModel
            }, ct);
            await ChangeLoanStatus(new ChangeLoanStatusModel()
            {
                Id = createdLoan.Id,
                UserId = userId,
                Note = refinanceLoans.Note,
                Status = Domain.Enums.LoanStatus.Started,
                RefinanceDate = refinanceLoans.RefinanceDate
            }, ct);

            var childLoanId = createdLoan.Id;
            foreach (var loanToRefinance in refinanceLoans.LoansToRefinance)
            {
                loanRelations.Add(new LoanRelationsEntity()
                {
                    Amount = loanToRefinance.Amount,
                    ChildLoanId = childLoanId,
                    ParentLoanId = loanToRefinance.LoanId,
                    RelationType = refinanceLoans.RelationType,
                    CreatedBy = userId
                });

                await ChangeLoanStatus(new ChangeLoanStatusModel()
                {
                    Id = loanToRefinance.LoanId,
                    UserId = userId,
                    Note = refinanceLoans.Note,
                    Status = Domain.Enums.LoanStatus.Refinanced,
                    RefinanceDate = refinanceLoans.RefinanceDate
                }, ct);
            }
            await _loanRepository.AddLoanRelations(loanRelations, ct);
            return Mapper.Map<Loan>(createdLoan);
        }

        [ExcludeFromCodeCoverage]
        public async Task<Loan> ReplaceLoanReceivables(IEnumerable<LoanReceivable> loanReceivables, Guid loanId, CancellationToken ct)
        {
            var loan = await _loanRepository.GetByIdWithTracking(loanId, ct);
            if (loan is null) throw new VariableNullException(ExceptionConstants.LoanNullMessage);

            loan.LoanReceivables = Mapper.Map<List<LoanReceivableEntity>>(loanReceivables);

            return Mapper.Map<Loan>(await _loanRepository.UpdateWithPayments(loan, ct));
        }

        public async Task<IEnumerable<Loan>?> GetByEinHashAndStatus(LoansByEinHashAndStatusFilter filter, bool? detailed, CancellationToken ct)
        {
            var repositoryFilter = Mapper.Map<RepositoryLoansByEinHashAndStatusFilter>(filter);

            var loanEntities = await _loanRepository.GetByEinHashAndStatus(repositoryFilter, ct);
            var loans = Mapper.Map<IEnumerable<Loan>?>(loanEntities);
            CalculateLoanDetails(loans, detailed);
            return loans;
        }

        private static decimal GetUpdatedLoanFee(IEnumerable<LoanReceivableEntity> updatedLoanReceivables)
        {
            return updatedLoanReceivables
                .Where(x => x.Type == Domain.Enums.ReceivableType.LoanFee)
                .Sum(i => i.ExpectedAmount);
        }

        public async Task HardDeleteByCompanyId(string companyId, DateTime date, CancellationToken cancellationToken)
        {
            var loansToDelete = await _loanRepository.Get(
                loan => loan.CompanyId == companyId && loan.CreatedAt < date.ToUniversalTime(), cancellationToken);

            await _loanRepository.HardDeleteRange(loansToDelete, cancellationToken);
        }

        public async Task<PaginatedResponse<Loan>> GetByQuery(PaginatedLoanQuery queryParams, CancellationToken ct)
        {
            var loans = Mapper.Map<PaginatedResponse<Loan>>(await _loanRepository.GetByQuery(queryParams, ct));
            CalculateLoanDetails(loans.Result, queryParams.Detailed);

            return loans;
        }

        private void CalculateLoanDetails(IEnumerable<Loan> loans, bool? detailed)
        {
            if (detailed.HasValue && detailed.Value)
            {
                loans.ForEach(loan =>
                {
                    loan.LoanReceivables = loan.LoanReceivables.GetOrdered();
                    loan.LoanDetails = _loanDetailsService.CalculateLoanDetails(loan);
                    loan.CalculateLoanPayablesDetails();
                });
            }
        }

        private void CalculateLoanDetails(Loan loan, bool? detailed)
        {
            if (detailed.HasValue && detailed.Value)
            {
                loan.LoanDetails = _loanDetailsService.CalculateLoanDetails(loan);
            }

            loan.CalculateLoanPayablesDetails();
        }

        public async Task<Loan> SetupLoanSettings(Guid loanId, string userId, LoanSettingsModel loanSettingsModel, CancellationToken ct)
        {
            var loanEntity = await _loanRepository.GetByIdWithTracking(loanId, ct)
                ?? throw new VariableNullException(ExceptionConstants.LoanNullMessage);

            if (loanSettingsModel.SkipLateFeeGeneration is not null && loanEntity.SkipLateFeeGeneration != loanSettingsModel.SkipLateFeeGeneration.Value)
            {
                await _changeLogService.Add(new ChangeLog()
                {
                    ChangeAt = DateTime.UtcNow,
                    EntityName = nameof(loanEntity),
                    PropertyName = nameof(loanEntity.SkipLateFeeGeneration),
                    OldValue = loanEntity.SkipLateFeeGeneration.ToString(),
                    NewValue = loanSettingsModel.SkipLateFeeGeneration.ToString(),
                    Note = loanSettingsModel.Note,
                    UserId = userId,
                    LoanId = loanEntity.Id,
                    PrimaryKeyValue = loanEntity.Id.ToString(),
                }, ct);

                loanEntity.SkipLateFeeGeneration = loanSettingsModel.SkipLateFeeGeneration.Value;
            }

            if (loanSettingsModel.SkipPenaltyGeneration is not null && loanEntity.SkipPenaltyGeneration != loanSettingsModel.SkipPenaltyGeneration.Value)
            {
                await _changeLogService.Add(new ChangeLog()
                {
                    ChangeAt = DateTime.UtcNow,
                    EntityName = nameof(loanEntity),
                    PropertyName = nameof(loanEntity.SkipPenaltyGeneration),
                    OldValue = loanEntity.SkipPenaltyGeneration.ToString(),
                    NewValue = loanSettingsModel.SkipPenaltyGeneration.ToString(),
                    Note = loanSettingsModel.Note,
                    UserId = userId,
                    LoanId = loanEntity.Id,
                    PrimaryKeyValue = loanEntity.Id.ToString(),
                }, ct);

                loanEntity.SkipPenaltyGeneration = loanSettingsModel.SkipPenaltyGeneration.Value;
            }

            return Mapper.Map<Loan>(await _loanRepository.Update(loanEntity, ct));
        }
    }
}