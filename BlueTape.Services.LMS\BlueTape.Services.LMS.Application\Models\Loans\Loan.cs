﻿using BlueTape.OBS.Enums;
using BlueTape.Services.LMS.Application.Models.AuthorizationPeriods;
using BlueTape.Services.LMS.Application.Models.Credits;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.LMS.Application.Models.LoanTemplates;
using BlueTape.Services.LMS.Application.Models.Payments;
using BlueTape.Services.LMS.Domain.Enums;
using System;
using System.Collections.Generic;

namespace BlueTape.Services.LMS.Application.Models.Loans
{
    public class Loan
    {
        public Guid Id { get; set; }
        public string? CompanyId { get; set; }
        public string? CompanyName { get; set; }
        public string? EinHash { get; set; }
        public string? ProjectId { get; set; }
        public decimal Amount { get; set; }
        public decimal RefundAmount { get; set; }
        public decimal Fee { get; set; }
        public Guid? ActiveLoanTemplateId { get; set; }
        public List<LoanParameters.LoanParameters> LoanParameters { get; set; } = null!;
        public LoanTemplate? ActiveLoanTemplate { get; set; }
        public LoanDetails? LoanDetails { get; set; }
        public List<LoanReceivable> LoanReceivables { get; set; } = new();
        public List<Payment> Payments { get; set; } = new();
        public Guid? CreditId { get; set; }
        public Credit? Credit { get; set; }
        public Guid? PaymentProcessTemplateId { get; set; }
        public Guid? AuthorizationPeriodId { get; set; }
        public AuthorizationPeriodModel? AuthorizationPeriod { get; set; }
        public LoanStatus Status { get; set; }
        public LoanOrigin? LoanOrigin { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsOverdue { get; set; }
        public DateOnly StartDate { get; set; }
        public DateOnly? CloseDate { get; set; } = null!;
        public DateOnly? DefaultedDate { get; set; } = null!;
        public DateOnly? LastPaymentDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastSyncDate { get; set; }
        public string? DrawApprovalId { get; set; }
        public int PayablesCount { get; set; }
        public string? PayablesNumber { get; set; }
        public decimal PayableAmount { get; set; }
        public List<LoanPayables.LoanPayables> LoanPayables { get; set; } = new();
        public string? MerchantId { get; set; }
        public string? MerchantName { get; set; }
        public FundingSource FundingSource { get; set; }
        public bool SkipLateFeeGeneration { get; set; }
        public bool SkipPenaltyGeneration { get; set; }
        public DownPaymentStatus DownPaymentStatus { get; set; }
        public DateTime? DownPaymentStatusAt { get; set; }
    }
}
