﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.1.32421.90
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{D5033862-DA40-4CE6-AE09-5B22A3D718FA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "nuget", "nuget", "{F9FF4E1E-BFE9-4E3C-A942-7CAB361A727F}"
	ProjectSection(SolutionItems) = preProject
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.UnitOfWork", "BlueTape.Services.LMS.UnitOfWork\BlueTape.Services.LMS.UnitOfWork.csproj", "{3D440858-2367-4A96-855F-7F93A449CC30}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.API.Tests", "BlueTape.Services.LMS.API.Tests\BlueTape.Services.LMS.API.Tests.csproj", "{43589FB8-D583-43D7-8D6C-D8C5E931C650}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.Application.Tests", "BlueTape.Services.LMS.Application.Tests\BlueTape.Services.LMS.Application.Tests.csproj", "{C97825F1-C80F-4483-A156-E9A0EB418F50}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.IntegrationTests", "BlueTape.Services.LMS.IntegrationTests\BlueTape.Services.LMS.IntegrationTests.csproj", "{87C3D140-B18E-471A-9801-2BA0EEAD10BC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.API", "BlueTape.Services.LMS.API\BlueTape.Services.LMS.API.csproj", "{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.Infrastructure", "BlueTape.Services.LMS.Infrastructure\BlueTape.Services.LMS.Infrastructure.csproj", "{5FDED342-264A-4B9D-8C14-24C85EB89EE6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.Application", "BlueTape.Services.LMS.Application\BlueTape.Services.LMS.Application.csproj", "{0533DD8A-67A3-4B67-B55B-706736B3C199}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.Domain", "BlueTape.Services.LMS.Domain\BlueTape.Services.LMS.Domain.csproj", "{DC714255-5268-4C1F-9A65-FFCD95603960}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.DataAccess", "BlueTape.Services.LMS.DataAccess\BlueTape.Services.LMS.DataAccess.csproj", "{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.DataAccess.SOFR", "BlueTape.Services.LMS.DataAccess.SOFR\BlueTape.Services.LMS.DataAccess.SOFR.csproj", "{AF1E047D-0ADF-4BEC-B6FE-C517D4F4170E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.DataAccess.SOFR.Tests", "BlueTape.Services.LMS.DataAccess.SOFR.Tests\BlueTape.Services.LMS.DataAccess.SOFR.Tests.csproj", "{853503F4-02BE-48AC-BE6B-5482C3B5C566}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.ARS", "BlueTape.Services.ARS\BlueTape.Services.ARS.csproj", "{61539520-681E-47E4-8270-6E06393F488C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.ARS.DataAccess", "BlueTape.Services.ARS.DataAccess\BlueTape.Services.ARS.DataAccess.csproj", "{F165331A-D3EB-43A1-B51B-13AE5551C5C4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.ARS.Models", "BlueTape.Services.ARS.Models\BlueTape.Services.ARS.Models.csproj", "{9CDF9773-5D12-40C2-A23A-6F6C0CE2FDF4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.ARS.Tests", "BlueTape.Services.ARS.Tests\BlueTape.Services.ARS.Tests.csproj", "{FEECAE8E-8D30-441E-8377-4F00E08A9CB9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.ARS.IntegrationTests", "BlueTape.Services.ARS.IntegrationTests\BlueTape.Services.ARS.IntegrationTests.csproj", "{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Functions", "Functions", "{07FF319C-C306-454B-82CC-0386CBD34573}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Functions.LMS.OverDueDetector", "Functions\BlueTape.Functions.LMS.OverDueDetector\BlueTape.Functions.LMS.OverDueDetector.csproj", "{B75391C9-B056-450E-96E8-D3AE980AF7A6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Functions.LMS.PenaltyDetector", "Functions\BlueTape.Functions.LMS.PenaltyDetector\BlueTape.Functions.LMS.PenaltyDetector.csproj", "{387B9567-5260-4169-9D7D-5653131DC7F5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.DataAccess.Company", "BlueTape.Services.LMS.DataAccess.Company\BlueTape.Services.LMS.DataAccess.Company.csproj", "{BE466A1E-7F8F-4B9B-B2A3-BC6F1C817970}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Functions.LMS.CreditStatusDetector", "Functions\BlueTape.Functions.LMS.CreditStatusDetector\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "{42A2F414-9839-4E50-BEE6-811F288E2932}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.OBS.Client", "BlueTape.OBS.Client\BlueTape.OBS.Client.csproj", "{F078E69F-E496-4D37-BDB4-C691A1182022}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.DataAccess.Mongo", "BlueTape.DataAccess.Mongo\BlueTape.DataAccess.Mongo.csproj", "{68F42AD4-F4F0-4351-AE01-E7D8A7A4B3FD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.Reporting.Domain", "BlueTape.Services.Reporting.Domain\BlueTape.Services.Reporting.Domain.csproj", "{60E5BF69-966D-4D0B-B24A-7E94AECA16F5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.Reporting.DataAccess", "BlueTape.Services.Reporting.DataAccess\BlueTape.Services.Reporting.DataAccess.csproj", "{B9949E43-CE68-4EC9-A6F1-E9F63C130385}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.Reporting", "BlueTape.Services.Reporting\BlueTape.Services.Reporting.csproj", "{FDEEF294-4CC1-42AA-9FE9-D96AB492DFEC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LoanReports.Tests", "BlueTape.Services.LoanReports.Tests\BlueTape.Services.LoanReports.Tests.csproj", "{380B87C4-79F9-420D-8F25-F9EDA2A44E41}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.DataAccess.External", "BlueTape.Services.DataAccess.External\BlueTape.Services.DataAccess.External.csproj", "{6897C904-37B9-4577-B296-6F73266A150B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Reports", "Reports", "{AEA6FC62-586E-4BE4-8E71-0B474043270D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.LMS.Infrastructure.Hosting", "BlueTape.Services.LMS.Infrastructure.Hosting\BlueTape.Services.LMS.Infrastructure.Hosting.csproj", "{139CF0AC-08EB-457B-9A86-A98CB27D0111}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Functions.LMS.Reports.LoanTapeReport", "Functions\Reports\BlueTape.Functions.LMS.Reports.LoanTapeReport\BlueTape.Functions.LMS.Reports.LoanTapeReport.csproj", "{E6238711-469E-4DB3-B188-58DE35BB294D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueTape.Services.LMS.MonitoringService", "BlueTape.Services.LMS.MonitoringService\BlueTape.Services.LMS.MonitoringService.csproj", "{72D63805-33E7-45BD-832E-243B283E9360}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueTape.Services.LMS.MonitoringService.Tests", "BlueTape.Services.LMS.MonitoringService.Tests\BlueTape.Services.LMS.MonitoringService.Tests.csproj", "{CDFE5A2E-B34B-4F85-9135-B7B01B8E7D78}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3D440858-2367-4A96-855F-7F93A449CC30}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D440858-2367-4A96-855F-7F93A449CC30}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D440858-2367-4A96-855F-7F93A449CC30}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D440858-2367-4A96-855F-7F93A449CC30}.Release|Any CPU.Build.0 = Release|Any CPU
		{43589FB8-D583-43D7-8D6C-D8C5E931C650}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{43589FB8-D583-43D7-8D6C-D8C5E931C650}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{43589FB8-D583-43D7-8D6C-D8C5E931C650}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{43589FB8-D583-43D7-8D6C-D8C5E931C650}.Release|Any CPU.Build.0 = Release|Any CPU
		{C97825F1-C80F-4483-A156-E9A0EB418F50}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C97825F1-C80F-4483-A156-E9A0EB418F50}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C97825F1-C80F-4483-A156-E9A0EB418F50}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C97825F1-C80F-4483-A156-E9A0EB418F50}.Release|Any CPU.Build.0 = Release|Any CPU
		{87C3D140-B18E-471A-9801-2BA0EEAD10BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{87C3D140-B18E-471A-9801-2BA0EEAD10BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{87C3D140-B18E-471A-9801-2BA0EEAD10BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{87C3D140-B18E-471A-9801-2BA0EEAD10BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}.Release|Any CPU.Build.0 = Release|Any CPU
		{5FDED342-264A-4B9D-8C14-24C85EB89EE6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5FDED342-264A-4B9D-8C14-24C85EB89EE6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5FDED342-264A-4B9D-8C14-24C85EB89EE6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5FDED342-264A-4B9D-8C14-24C85EB89EE6}.Release|Any CPU.Build.0 = Release|Any CPU
		{0533DD8A-67A3-4B67-B55B-706736B3C199}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0533DD8A-67A3-4B67-B55B-706736B3C199}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0533DD8A-67A3-4B67-B55B-706736B3C199}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0533DD8A-67A3-4B67-B55B-706736B3C199}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC714255-5268-4C1F-9A65-FFCD95603960}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC714255-5268-4C1F-9A65-FFCD95603960}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC714255-5268-4C1F-9A65-FFCD95603960}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC714255-5268-4C1F-9A65-FFCD95603960}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF1E047D-0ADF-4BEC-B6FE-C517D4F4170E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF1E047D-0ADF-4BEC-B6FE-C517D4F4170E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF1E047D-0ADF-4BEC-B6FE-C517D4F4170E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF1E047D-0ADF-4BEC-B6FE-C517D4F4170E}.Release|Any CPU.Build.0 = Release|Any CPU
		{853503F4-02BE-48AC-BE6B-5482C3B5C566}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{853503F4-02BE-48AC-BE6B-5482C3B5C566}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{853503F4-02BE-48AC-BE6B-5482C3B5C566}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{853503F4-02BE-48AC-BE6B-5482C3B5C566}.Release|Any CPU.Build.0 = Release|Any CPU
		{61539520-681E-47E4-8270-6E06393F488C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{61539520-681E-47E4-8270-6E06393F488C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{61539520-681E-47E4-8270-6E06393F488C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{61539520-681E-47E4-8270-6E06393F488C}.Release|Any CPU.Build.0 = Release|Any CPU
		{F165331A-D3EB-43A1-B51B-13AE5551C5C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F165331A-D3EB-43A1-B51B-13AE5551C5C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F165331A-D3EB-43A1-B51B-13AE5551C5C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F165331A-D3EB-43A1-B51B-13AE5551C5C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{9CDF9773-5D12-40C2-A23A-6F6C0CE2FDF4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9CDF9773-5D12-40C2-A23A-6F6C0CE2FDF4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9CDF9773-5D12-40C2-A23A-6F6C0CE2FDF4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9CDF9773-5D12-40C2-A23A-6F6C0CE2FDF4}.Release|Any CPU.Build.0 = Release|Any CPU
		{FEECAE8E-8D30-441E-8377-4F00E08A9CB9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FEECAE8E-8D30-441E-8377-4F00E08A9CB9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FEECAE8E-8D30-441E-8377-4F00E08A9CB9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FEECAE8E-8D30-441E-8377-4F00E08A9CB9}.Release|Any CPU.Build.0 = Release|Any CPU
		{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}.Release|Any CPU.Build.0 = Release|Any CPU
		{B75391C9-B056-450E-96E8-D3AE980AF7A6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B75391C9-B056-450E-96E8-D3AE980AF7A6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B75391C9-B056-450E-96E8-D3AE980AF7A6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B75391C9-B056-450E-96E8-D3AE980AF7A6}.Release|Any CPU.Build.0 = Release|Any CPU
		{387B9567-5260-4169-9D7D-5653131DC7F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{387B9567-5260-4169-9D7D-5653131DC7F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{387B9567-5260-4169-9D7D-5653131DC7F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{387B9567-5260-4169-9D7D-5653131DC7F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE466A1E-7F8F-4B9B-B2A3-BC6F1C817970}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE466A1E-7F8F-4B9B-B2A3-BC6F1C817970}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE466A1E-7F8F-4B9B-B2A3-BC6F1C817970}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE466A1E-7F8F-4B9B-B2A3-BC6F1C817970}.Release|Any CPU.Build.0 = Release|Any CPU
		{42A2F414-9839-4E50-BEE6-811F288E2932}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{42A2F414-9839-4E50-BEE6-811F288E2932}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{42A2F414-9839-4E50-BEE6-811F288E2932}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{42A2F414-9839-4E50-BEE6-811F288E2932}.Release|Any CPU.Build.0 = Release|Any CPU
		{F078E69F-E496-4D37-BDB4-C691A1182022}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F078E69F-E496-4D37-BDB4-C691A1182022}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F078E69F-E496-4D37-BDB4-C691A1182022}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F078E69F-E496-4D37-BDB4-C691A1182022}.Release|Any CPU.Build.0 = Release|Any CPU
		{68F42AD4-F4F0-4351-AE01-E7D8A7A4B3FD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{68F42AD4-F4F0-4351-AE01-E7D8A7A4B3FD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{68F42AD4-F4F0-4351-AE01-E7D8A7A4B3FD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{68F42AD4-F4F0-4351-AE01-E7D8A7A4B3FD}.Release|Any CPU.Build.0 = Release|Any CPU
		{60E5BF69-966D-4D0B-B24A-7E94AECA16F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{60E5BF69-966D-4D0B-B24A-7E94AECA16F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{60E5BF69-966D-4D0B-B24A-7E94AECA16F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{60E5BF69-966D-4D0B-B24A-7E94AECA16F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{B9949E43-CE68-4EC9-A6F1-E9F63C130385}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B9949E43-CE68-4EC9-A6F1-E9F63C130385}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B9949E43-CE68-4EC9-A6F1-E9F63C130385}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B9949E43-CE68-4EC9-A6F1-E9F63C130385}.Release|Any CPU.Build.0 = Release|Any CPU
		{FDEEF294-4CC1-42AA-9FE9-D96AB492DFEC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FDEEF294-4CC1-42AA-9FE9-D96AB492DFEC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FDEEF294-4CC1-42AA-9FE9-D96AB492DFEC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FDEEF294-4CC1-42AA-9FE9-D96AB492DFEC}.Release|Any CPU.Build.0 = Release|Any CPU
		{380B87C4-79F9-420D-8F25-F9EDA2A44E41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{380B87C4-79F9-420D-8F25-F9EDA2A44E41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{380B87C4-79F9-420D-8F25-F9EDA2A44E41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{380B87C4-79F9-420D-8F25-F9EDA2A44E41}.Release|Any CPU.Build.0 = Release|Any CPU
		{6897C904-37B9-4577-B296-6F73266A150B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6897C904-37B9-4577-B296-6F73266A150B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6897C904-37B9-4577-B296-6F73266A150B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6897C904-37B9-4577-B296-6F73266A150B}.Release|Any CPU.Build.0 = Release|Any CPU
		{139CF0AC-08EB-457B-9A86-A98CB27D0111}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{139CF0AC-08EB-457B-9A86-A98CB27D0111}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{139CF0AC-08EB-457B-9A86-A98CB27D0111}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{139CF0AC-08EB-457B-9A86-A98CB27D0111}.Release|Any CPU.Build.0 = Release|Any CPU
		{E6238711-469E-4DB3-B188-58DE35BB294D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E6238711-469E-4DB3-B188-58DE35BB294D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E6238711-469E-4DB3-B188-58DE35BB294D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E6238711-469E-4DB3-B188-58DE35BB294D}.Release|Any CPU.Build.0 = Release|Any CPU
		{72D63805-33E7-45BD-832E-243B283E9360}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{72D63805-33E7-45BD-832E-243B283E9360}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{72D63805-33E7-45BD-832E-243B283E9360}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{72D63805-33E7-45BD-832E-243B283E9360}.Release|Any CPU.Build.0 = Release|Any CPU
		{CDFE5A2E-B34B-4F85-9135-B7B01B8E7D78}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CDFE5A2E-B34B-4F85-9135-B7B01B8E7D78}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CDFE5A2E-B34B-4F85-9135-B7B01B8E7D78}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CDFE5A2E-B34B-4F85-9135-B7B01B8E7D78}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{43589FB8-D583-43D7-8D6C-D8C5E931C650} = {D5033862-DA40-4CE6-AE09-5B22A3D718FA}
		{C97825F1-C80F-4483-A156-E9A0EB418F50} = {D5033862-DA40-4CE6-AE09-5B22A3D718FA}
		{87C3D140-B18E-471A-9801-2BA0EEAD10BC} = {D5033862-DA40-4CE6-AE09-5B22A3D718FA}
		{853503F4-02BE-48AC-BE6B-5482C3B5C566} = {D5033862-DA40-4CE6-AE09-5B22A3D718FA}
		{FEECAE8E-8D30-441E-8377-4F00E08A9CB9} = {D5033862-DA40-4CE6-AE09-5B22A3D718FA}
		{5DA9CB35-7181-4B23-8DF7-7E351B973F9B} = {D5033862-DA40-4CE6-AE09-5B22A3D718FA}
		{B75391C9-B056-450E-96E8-D3AE980AF7A6} = {07FF319C-C306-454B-82CC-0386CBD34573}
		{387B9567-5260-4169-9D7D-5653131DC7F5} = {07FF319C-C306-454B-82CC-0386CBD34573}
		{42A2F414-9839-4E50-BEE6-811F288E2932} = {07FF319C-C306-454B-82CC-0386CBD34573}
		{380B87C4-79F9-420D-8F25-F9EDA2A44E41} = {D5033862-DA40-4CE6-AE09-5B22A3D718FA}
		{AEA6FC62-586E-4BE4-8E71-0B474043270D} = {07FF319C-C306-454B-82CC-0386CBD34573}
		{E6238711-469E-4DB3-B188-58DE35BB294D} = {AEA6FC62-586E-4BE4-8E71-0B474043270D}
		{CDFE5A2E-B34B-4F85-9135-B7B01B8E7D78} = {D5033862-DA40-4CE6-AE09-5B22A3D718FA}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {63B2528F-758F-43F9-94FB-BB641527EC49}
	EndGlobalSection
EndGlobal
