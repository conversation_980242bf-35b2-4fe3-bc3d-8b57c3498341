﻿using AutoFilter;
using BlueTape.OBS.Enums;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories.ChangeLog;
using BlueTape.Services.LMS.DataAccess.Contexts;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Entities.Filters;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Utilities.Enums;
using BlueTape.Utilities.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Buffers;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using TinyHelpers.Extensions;

namespace BlueTape.Services.LMS.DataAccess.Repositories
{
    public class LoanRepository : GenericRepository<LoanEntity>, ILoanRepository
    {
        private readonly SyncOptions _syncOptions;


        public LoanRepository(IOptions<SyncOptions> syncOptions, DatabaseContext context,
            IChangeLoggedGenericRepository<LoanEntity> changeLoggedGenericRepository)
            : base(context, changeLoggedGenericRepository)
        {
            _syncOptions = syncOptions.Value;
        }

        public override async Task<IEnumerable<LoanEntity>> Get(CancellationToken ct)
        {
            return await DbSet.AsNoTracking()
                .Include(x => x.LoanPayables)
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.LoanParameters
                    .Where(parameters => parameters.IsActive))
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .Include(x => x.Payments)
                .OrderBy(p => p.StartDate)
                .ToListAsync(ct);
        }
        public override async Task<IEnumerable<LoanEntity>> Get(Expression<Func<LoanEntity, bool>> predicate, CancellationToken ct)
        {
            return await DbSet
                .AsNoTracking()
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.Payments)
                .OrderBy(p => p.StartDate)
                .Include(x => x.LoanParameters)
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .ThenInclude(l => l.LatePaymentFeeDetails)
                .Include(x => x.Credit)
                .Include(x => x.LoanPayables)
                .Where(predicate)
                .ToListAsync(ct);
        }

        public override Task<LoanEntity?> GetById(Guid id, CancellationToken ct)
        {
            return DbSet
                .AsNoTracking()
                .Include(x => x.LoanPayables)
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.Payments)
                .OrderBy(p => p.StartDate)
                .Include(x => x.LoanParameters)
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .ThenInclude(x => x.LoanReceivablesPayments)
                .Include(x => x.LoanReceivables)
                .ThenInclude(x => x.LatePaymentFeeDetails)
                .Include(x => x.Credit)
                .Include(x => x.LoanReceivables)
                .ThenInclude(x => x.PenaltyInterestDetails)
                .FirstOrDefaultAsync(x => x.Id == id, ct);
        }

        public Task<LoanEntity?> GetByIdWithTracking(Guid id, CancellationToken ct)
        {
            return DbSet
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.LoanPayables)
                .Include(x => x.Payments)
                .OrderBy(p => p.StartDate)
                .Include(x => x.LoanParameters
                    .Where(parameters => parameters.IsActive))
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .ThenInclude(x => x.LoanReceivablesPayments)
                .FirstOrDefaultAsync(x => x.Id == id, ct);
        }

        public async Task<IEnumerable<LoanEntity>?> GetPaid(DateOnly fromDate, DateOnly toDate, CancellationToken ct)
        {
            return await DbSet.AsNoTracking()
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.LoanPayables)
                .Include(x => x.Payments)
                .OrderBy(p => p.StartDate)
                .Include(x => x.LoanReceivables
                    .OrderByDescending(p => p.PaidDate))
                .Where(l => (l.LoanReceivables
                                 .Where(r => r.Status != LoanReceivableStatus.Canceled && r.Status != LoanReceivableStatus.None
                                                                                       && r.ScheduleStatus == ScheduleStatus.Current)
                                 .All(i => i.Status == LoanReceivableStatus.Paid)
                             && l.LastPaymentDate >= fromDate && l.LastPaymentDate <= toDate))
                .ToListAsync(ct);
        }

        public async Task<IEnumerable<LoanEntity>?> GetOverdue(DateOnly fromDate, DateOnly toDate, CancellationToken ct)
        {
            return await DbSet.AsNoTracking()
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.Payments)
                .Include(x => x.LoanPayables)
                .OrderBy(p => p.StartDate)
                .Include(x => x.LoanReceivables
                    .OrderByDescending(p => p.PaidDate))
                .Where(l => l.LastPaymentDate >= fromDate && l.LastPaymentDate <= toDate && l.IsOverdue)
                .ToListAsync(ct);
        }

        public Task ChangeLoanStatus(Guid id, LoanStatus loanStatus, CancellationToken ct)
        {
            LoanEntity entity = new()
            {
                Id = id,
                Status = loanStatus
            };
            entity.UpdatedAt = DateTime.UtcNow;
            Context.Entry(entity).Property(entity => entity.Status).IsModified = true;

            return Context.SaveChangesAsync(ct);
        }

        public async Task<IEnumerable<LoanEntity>> UpdateRangeWithPayments(IEnumerable<LoanEntity> entities, CancellationToken ct)
        {
            foreach (var entity in entities)
            {
                entity.UpdatedAt = DateTime.UtcNow;
            }
            DbSet.UpdateRange(entities);
            await Context.SaveChangesAsync(ct);

            return entities;
        }

        public async Task<LoanEntity?> UpdateWithPayments(LoanEntity entity, CancellationToken ct, ChangeLogEntity? changeLog = null)
        {
            if (changeLog != null)
                await ChangeLoggedGenericRepository.Update(entity, changeLog, ct);

            DbSet.Update(entity);
            entity.UpdatedAt = DateTime.UtcNow;
            entity.LoanReceivables.ForEach(i => i.UpdatedAt = DateTime.UtcNow);
            await Context.SaveChangesAsync(ct);

            return entity;
        }

        public async Task<IEnumerable<LoanEntity>> UpdateRangeSyncTime(IEnumerable<LoanEntity> entities, CancellationToken ct)
        {
            foreach (var entity in entities)
            {
                entity.LastSyncDate = DateTime.UtcNow;
            }
            DbSet.UpdateRange(entities);
            await Context.SaveChangesAsyncExcludingUpdatedAt(ct);

            return entities;
        }

        public override Task Delete(LoanEntity entity, CancellationToken ct)
        {
            entity.IsDeleted = true;
            Context.Entry(entity).State = EntityState.Modified;

            return Context.SaveChangesAsync(ct);
        }

        public override Task Delete(Guid id, CancellationToken ct)
        {
            LoanEntity entity = new()
            {
                Id = id,
                IsDeleted = true
            };
            Context.Entry(entity).Property(x => x.IsDeleted).IsModified = true;

            return Context.SaveChangesAsync(ct);
        }

        public Task HardDeleteRange(IEnumerable<LoanEntity> loansToDelete, CancellationToken ct)
        {
            DbSet.RemoveRange(loansToDelete);
            return Context.SaveChangesAsync(ct);
        }

        public async Task<IEnumerable<LoanEntity>?> GetUpcomingAndOverdueLoans(DateOnly date, CancellationToken ct)
        {
            return await DbSet.AsNoTracking()
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.Payments)
                .Include(x => x.LoanPayables)
                .OrderBy(p => p.StartDate)
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .Where(i => (i.Status == LoanStatus.Started || i.Status == LoanStatus.Pending) && !i.IsDeleted &&
                            i.LoanReceivables.Any(x => x.ExpectedDate <= date && x.PaidAmount != x.ExpectedAmount) && i.LoanOrigin != LoanOrigin.Factoring)
                .ToListAsync(ct);
        }

        public async Task<IEnumerable<LoanEntity>?> GetByIds(IEnumerable<Guid> ids, CancellationToken ct)
        {
            return await DbSet.AsNoTracking()
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.Payments)
                .Include(x => x.LoanPayables)
                .OrderBy(p => p.StartDate)
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .Where(i => ids.Contains(i.Id))
                .Include(i => i.LoanParameters)
                .ToListAsync(ct);
        }

        public override async Task<LoanEntity> Add(LoanEntity entity, CancellationToken ct, ChangeLogEntity? changeLogEntity = null)
        {
            if (changeLogEntity is not null)
            {
                return await ChangeLoggedGenericRepository.Add(entity, changeLogEntity, ct);
            }
            entity.LoanReceivables.ForEach(i => i.CreatedAt = DateTime.UtcNow);
            entity.CreatedAt = DateTime.UtcNow;

            await DbSet.AddAsync(entity, ct);
            await Context.SaveChangesAsync(ct);

            return entity;
        }

        public async Task<IEnumerable<LoanRelationsEntity>> AddLoanRelations(IEnumerable<LoanRelationsEntity> loanRelations, CancellationToken ct)
        {
            foreach (var loanRelation in loanRelations)
            {
                loanRelation.CreatedAt = DateTime.UtcNow;
            }
            await Context.LoanRelations!.AddRangeAsync(loanRelations, ct);
            await Context.SaveChangesAsync(ct);

            return loanRelations;
        }

        public async Task<IEnumerable<LoanRelationsEntity>> GetLoanRelations(Expression<Func<LoanRelationsEntity, bool>> predicate, CancellationToken ct)
        {
            var loanRelations = await Context.LoanRelations!.AsNoTracking().Where(predicate).ToListAsync(ct);
            return loanRelations ?? new List<LoanRelationsEntity>();
        }

        public async Task<IEnumerable<LoanEntity>?> GetLoansToSync(CancellationToken ct)
        {
            var treshold = _syncOptions.LastSyncDateThresholdDifferenceInSeconds;
            return await DbSet
                .Include(x => x.LoanReceivables.OrderBy(x => x.ExpectedDate))
                .Where(loan => !loan.LastSyncDate.HasValue
                               || DateTime.Compare(loan.LastSyncDate.Value.AddSeconds(treshold), loan.UpdatedAt) < 0
                               || loan.LoanReceivables.Any(r => DateTime.Compare(loan.LastSyncDate.Value.AddSeconds(treshold), r.UpdatedAt) < 0))
                .Include(x => x.Payments)
                .ToListAsync(ct);
        }

        public Task<List<LoanEntity>> GetLoansWithoutParameters()
        {
            return DbSet
                .Where(x => x.LoanParameters.Count == 0)
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.LoanParameters)
                .ToListAsync();
        }

        public async Task<IReadOnlyCollection<LoanEntity>> GetLoansWithDetailedInfo(Expression<Func<LoanEntity, bool>> predicate, CancellationToken ct, int offset = 0, int count = int.MaxValue)
        {
            var query = DbSet
                .AsNoTrackingWithIdentityResolution()
                .Include(x => x.PaymentProcessTemplate)
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.Credit)
                .Include(x => x.Payments)
                .Include(x => x.LoanParameters
                    .Where(parameters => parameters.IsActive))
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .ThenInclude(l => l.LatePaymentFeeDetails)
                .Include(x => x.LoanReceivables)
                .ThenInclude(x => x.PenaltyInterestDetails)
                .Include(x => x.LoanPayables)
                .Where(x => !x.IsDeleted)
                .Where(predicate);

            return await query.Skip(offset).Take(count).ToListAsync(ct);
        }

        public async Task<IEnumerable<LoanEntity>?> GetByEinHashAndStatus(LoansByEinHashAndStatusFilter filter, CancellationToken ct)
        {
            return await DbSet
                .AsNoTracking()
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.LoanPayables)
                .Include(x => x.LoanReceivables
                    .OrderBy(r => r.ExpectedDate))
                .AutoFilter(filter)
                .ToListAsync(ct);
        }

        public Task<LoanEntity?> GetByPayableId(string payableId, CancellationToken ct)
        {
            return DbSet
                .AsNoTracking()
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.LoanPayables)
                .Where(x => x.LoanPayables.Any(payables => payables.PayableId == payableId))
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .Include(x => x.Payments)
                .Include(x => x.Credit)
                .FirstOrDefaultAsync(ct);
        }

        public async Task<IEnumerable<LoanEntity>?> GetByDownPaymentExpireDate(DateOnly fromDate, DateOnly toDate, CancellationToken ct)
        {
            var query = DbSet
             .AsNoTracking()
             .Include(x => x.LoanPayables)
             .Include(x => x.ActiveLoanTemplate)
             .Include(x => x.Payments)
             .Include(x => x.LoanParameters)
             .Include(x => x.LoanReceivables
                 .OrderBy(p => p.ExpectedDate))
             .Include(x => x.Credit)
             .AsQueryable();

            var downPaymentExpireStart = fromDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
            query = query.Where(x => x.LoanParameters.Any(t => t.DownPaymentExpireAt != null &&
               t.DownPaymentExpireAt >= downPaymentExpireStart));
            var downPaymentExpireEnd = toDate.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc);
            query = query.Where(x => x.LoanParameters.Any(t => t.DownPaymentExpireAt != null &&
                t.DownPaymentExpireAt <= downPaymentExpireEnd));

            return await query.ToListAsync(ct);
        }

        public async Task<IEnumerable<LoanEntity>?> GetByDownPaymentStatuses(string[] downPaymentStatuses, CancellationToken ct)
        {
            var statuses = downPaymentStatuses.Select(x => { Enum.TryParse<DownPaymentStatus>(x, true, out var result); return result; });

            return await DbSet
                .AsNoTrackingWithIdentityResolution()
                .Include(x => x.LoanPayables)
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.Payments)
                .Include(x => x.LoanParameters)
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .Include(x => x.Credit)
                .Where(x => statuses.Contains(x.DownPaymentStatus))
                .ToListAsync(ct);
        }

        public Task<LoanEntity?> GetByDrawApprovalId(string drawApprovalId, CancellationToken ct)
        {
            return DbSet
                .AsNoTracking()
                .Include(x => x.LoanPayables)
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.Payments)
                .Include(x => x.LoanParameters)
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .Include(x => x.Credit)
                .Where(x => x.DrawApprovalId == drawApprovalId)
                .FirstOrDefaultAsync(ct);
        }

        public async Task<IEnumerable<LoanEntity>> GetByDrawApprovalIds(string[] drawApprovalIds, CancellationToken ct)
        {
            return await DbSet
                .AsNoTracking()
                .Include(x => x.LoanPayables)
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.Payments)
                .Include(x => x.LoanParameters)
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .Include(x => x.Credit)
                .Where(x => drawApprovalIds.Contains(x.DrawApprovalId))
                .ToListAsync(ct);
        }

        public async Task<IEnumerable<LoanEntity>?> GetByProduct(ProductType product, CancellationToken ct)
        {
            return await DbSet
                .AsNoTracking()
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.LoanPayables)
                .Where(x => x.ActiveLoanTemplate != null && x.ActiveLoanTemplate.Product == product)
                .Include(x => x.LoanReceivables
                    .OrderBy(p => p.ExpectedDate))
                .Include(x => x.Payments)
                .Include(x => x.Credit)
                .ToListAsync(ct);
        }

        public Task<List<string?>> GetAllCompanyIds(CancellationToken ct)
        {
            return DbSet.AsNoTracking().Select(x => x.CompanyId).ToListAsync(ct);
        }

        public Task<List<string?>> GetAllCompanyIdsForLoansWithoutCredit(CancellationToken ct)
        {
            return DbSet.AsNoTracking().Include(x => x.Credit).Where(x => x.Credit == null || x.CreditId == null).Select(x => x.CompanyId).ToListAsync(ct);
        }

        public async Task<PaginatedResponse<LoanEntity>> GetByQuery(PaginatedLoanQuery queryParams, CancellationToken ct)
        {
            var query = DbSet.AsNoTracking()
                .Include(x => x.LoanPayables)
                .Include(x => x.ActiveLoanTemplate)
                .Include(x => x.LoanReceivables)
                .AsQueryable();

            if (queryParams.Detailed is not null && queryParams.Detailed.Value)
                query = query.Include(x => x.Payments)
                        .ThenInclude(x => x.LoanReceivablesPayments)
                        .Include(x => x.LoanParameters);

            if (queryParams.CompanyId is not null) query = query.Where(x => x.CompanyId == queryParams.CompanyId);
            if (queryParams.CustomerNameOrDba is not null) query = query.Where(x => !string.IsNullOrEmpty(x.CompanyName) && EF.Functions.Like(x.CompanyName.ToLower(), $"%{queryParams.CustomerNameOrDba.ToLower()}%"));

            var loanStatuses = queryParams.Status?.Select(x => { Enum.TryParse<LoanStatus>(x, true, out var result); return result; });
            if (loanStatuses is not null && loanStatuses.Any())
            {
                query = query.Where(x => loanStatuses.Contains(x.Status));
            }

            if (queryParams.Amount is not null) query = query.Where(x => x.Amount >= queryParams.Amount);
            if (queryParams.LoanType is not null)
                query = query.Where(x => x.ActiveLoanTemplate!.Type == queryParams.LoanType);
            if (queryParams.PayablesNumberOrMerchantName is not null)
            {
                var searchValue = queryParams.PayablesNumberOrMerchantName.ToLower();
                query = query.Where(x =>
                    (!string.IsNullOrEmpty(x.MerchantName) && EF.Functions.Like(x.MerchantName.ToLower(), $"%{searchValue}%")) ||
                    (!string.IsNullOrEmpty(x.CompanyName) && EF.Functions.Like(x.CompanyName.ToLower(), $"%{searchValue}%")) ||
                    x.LoanPayables.Any(payable =>
                        payable.InvoiceNumber != null && EF.Functions.Like(payable.InvoiceNumber.ToLower(), $"%{searchValue}%")));
            }

            if (queryParams.Product is not null)
                query = query.Where(x => x.ActiveLoanTemplate != null && x.ActiveLoanTemplate.Product == ToProductType(queryParams.Product));

            var loanTemplates = queryParams.TemplateType?.Select(x => { Enum.TryParse<TemplateType>(x, true, out var result); return result; });
            if (loanTemplates is not null && loanTemplates.Any())
            {
                query = query.Where(x => x.ActiveLoanTemplate != null && loanTemplates.Contains(x.ActiveLoanTemplate.TemplateType));
            }

            if (queryParams.Search is not null)
            {
                var searchValue = queryParams.Search.ToLower();
                query = query.Where(x =>
                    (!string.IsNullOrEmpty(x.MerchantName) && EF.Functions.Like(x.MerchantName.ToLower(), $"%{searchValue}%")) ||
                    (!string.IsNullOrEmpty(x.CompanyName) && EF.Functions.Like(x.CompanyName.ToLower(), $"%{searchValue}%")) ||
                    x.LoanPayables.Any(payable =>
                        payable.InvoiceNumber != null && EF.Functions.Like(payable.InvoiceNumber.ToLower(), $"%{searchValue}%")));
            }

            var loanOrigins = queryParams.LoanOrigin?.Select(x => { Enum.TryParse<LoanOrigin>(x, true, out var result); return result; });
            if (loanOrigins is not null && loanOrigins.Any())
            {
                query = query.Where(x => x.LoanOrigin != null && loanOrigins.Contains(x.LoanOrigin.Value));

                if (loanOrigins.Contains(LoanOrigin.Quote) && loanTemplates != null && !loanTemplates.Contains(TemplateType.VirtualCard))
                {
                    query = query.Where(x => x.ActiveLoanTemplate != null && x.ActiveLoanTemplate.TemplateType != TemplateType.VirtualCard);
                }
            }

            if (queryParams.ShowLateOnly is not null && queryParams.ShowLateOnly == true)
                query = query.Where(x => x.IsOverdue);

            if (queryParams.NextPaymentDateFrom is not null)
                query = query.Where(x => (x.Status == LoanStatus.Started || x.Status == LoanStatus.Pending) && !x.IsDeleted &&
                                         (x.LoanReceivables.Any(receivable => receivable.ExpectedDate >= queryParams.NextPaymentDateFrom && receivable.PaidAmount != receivable.ExpectedAmount)));
            if (queryParams.NextPaymentDateTo is not null)
                query = query.Where(x => (x.Status == LoanStatus.Started || x.Status == LoanStatus.Pending) && !x.IsDeleted &&
                                         (x.LoanReceivables.Any(x => x.ExpectedDate <= queryParams.NextPaymentDateTo && x.PaidAmount != x.ExpectedAmount)));
            if (queryParams.NextPaymentAmount is not null)
                query = query.Where(x =>
                    (x.Status == LoanStatus.Started || x.Status == LoanStatus.Pending) && !x.IsDeleted &&
                    x.LoanReceivables.Any(x => x.PaidAmount != x.ExpectedAmount && x.ExpectedAmount - x.ProcessingAmount >= queryParams.NextPaymentAmount));
            if (queryParams.PayablesAmount is not null)
                query = query.Where(x => x.LoanPayables.Any(payable => payable.Amount >= queryParams.PayablesAmount));

            if (queryParams.SearchIds is not null && queryParams.SearchIds.Any())
            {
                var searchIdType = queryParams.SearchIdType?.ToLowerInvariant();

                if (string.Equals(searchIdType, nameof(LoanEntity.DrawApprovalId).ToLowerInvariant(), StringComparison.OrdinalIgnoreCase) ||
                    string.Equals(searchIdType, "drawapprovalid", StringComparison.OrdinalIgnoreCase))
                {
                    query = query.Where(x => queryParams.SearchIds.Contains(x.DrawApprovalId));
                }
                else if (string.Equals(searchIdType, nameof(LoanReceivablesPaymentsEntity.LoanReceivableId).ToLowerInvariant(), StringComparison.OrdinalIgnoreCase) ||
                         string.Equals(searchIdType, "loanreceivableid", StringComparison.OrdinalIgnoreCase) ||
                         string.Equals(searchIdType, "receivableid", StringComparison.OrdinalIgnoreCase))
                {
                    var parsedIds = ParseGuids(queryParams.SearchIds);
                    query = query.Where(loan => parsedIds.Any(searchId => loan.LoanReceivables.Select(r => r.Id).Contains(searchId)));
                }
                else if (string.Equals(searchIdType, nameof(LoanPayablesEntity.PayableId).ToLowerInvariant(), StringComparison.OrdinalIgnoreCase) ||
                         string.Equals(searchIdType, "invoiceid", StringComparison.OrdinalIgnoreCase) ||
                         string.Equals(searchIdType, "payableid", StringComparison.OrdinalIgnoreCase))
                {
                    query = query.Where(loan => loan.LoanPayables.Any(payable => queryParams.SearchIds.Contains(payable.PayableId)));
                }
                else
                {
                    var parsedIds = ParseGuids(queryParams.SearchIds);
                    query = query.Where(x => parsedIds.Contains(x.Id));
                }
            }

            var downPaymentStatuses = queryParams.DownPaymentStatus?.Select(x => { Enum.TryParse<DownPaymentStatus>(x, true, out var result); return result; });
            if (downPaymentStatuses is not null && downPaymentStatuses.Any())
            {
                query = query.Where(x => downPaymentStatuses.Contains(x.DownPaymentStatus));
            }

            if (queryParams.DownPaymentExpireStart is not null)
            {
                var downPaymentExpireStart = queryParams.DownPaymentExpireStart.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
                query = query.Where(x => x.LoanParameters.Any(t => t.DownPaymentExpireAt != null &&
                   t.DownPaymentExpireAt >= downPaymentExpireStart));
            }
            if (queryParams.DownPaymentExpireEnd is not null)
            {
                var downPaymentExpireEnd = queryParams.DownPaymentExpireEnd.Value.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc);
                query = query.Where(x => x.LoanParameters.Any(t => t.DownPaymentExpireAt != null &&
                    t.DownPaymentExpireAt <= downPaymentExpireEnd));
            }

            query = AddOrdering(query, queryParams.SortBy, queryParams.SortOrder);

            var count = await query.CountAsync(ct);

            return new()
            {
                Offset = queryParams.PageNumber,
                Limit = queryParams.PageSize,
                Total = count,
                Count = (int)Math.Round((double)count / queryParams.PageSize, 0, MidpointRounding.ToPositiveInfinity),
                Result = await query
                    .Skip((queryParams.PageNumber - 1) * queryParams.PageSize)
                    .Take(queryParams.PageSize).ToListAsync(ct)
            };
        }

        public Task UpdatePaymentProcessTemplate(Guid loanId, Guid paymentProcessTemplateId, CancellationToken ct)
        {
            LoanEntity entity = new()
            {
                Id = loanId,
                PaymentProcessTemplateId = paymentProcessTemplateId
            };
            entity.UpdatedAt = DateTime.UtcNow;
            Context.Entry(entity).Property(entity => entity.PaymentProcessTemplateId).IsModified = true;

            return Context.SaveChangesAsync(ct);
        }


        public Task<List<Guid>> GetLoanIdsByExpression(Expression<Func<LoanEntity, bool>> predicate, CancellationToken ct)
        {
            return DbSet.AsNoTracking().Where(predicate).Select(x => x.Id).ToListAsync(ct);
        }

        private static IQueryable<LoanEntity> AddOrdering(IQueryable<LoanEntity> query, LoanQuerySortingParams? sortBy, SortOrderType? sortOrder)
        {
            //TODO sorting by NextPaymentAmount and Next Payment Date
            switch (sortBy)
            {
                case LoanQuerySortingParams.PayablesAmount:
                case LoanQuerySortingParams.Amount:
                    {
                        if (sortOrder is null || sortOrder == SortOrderType.asc) return query.OrderBy(x => x.Amount);
                        return query.OrderByDescending(x => x.Amount);
                    }
                case LoanQuerySortingParams.StartDate:
                    {
                        if (sortOrder is null || sortOrder == SortOrderType.asc) return query.OrderBy(x => x.StartDate);
                        return query.OrderByDescending(x => x.StartDate);
                    }
                case LoanQuerySortingParams.CreatedAt:
                    {
                        if (sortOrder is null || sortOrder == SortOrderType.asc) return query.OrderBy(x => x.CreatedAt);
                        return query.OrderByDescending(x => x.CreatedAt);
                    }
                case LoanQuerySortingParams.DownPaymentExpireAt:
                    {
                        if (sortOrder is null || sortOrder == SortOrderType.asc) return query.OrderBy(x => x.LoanParameters.FirstOrDefault(x => x.IsActive).DownPaymentAmount);
                        return query.OrderByDescending(x => x.CreatedAt);
                    }
                case LoanQuerySortingParams.NextPaymentAmount: return query;
                case LoanQuerySortingParams.NextPaymentDate: return query;
                case null: return query.OrderByDescending(x => x.CreatedAt);
                default: return query;
            }
        }

        private static ProductType ToProductType(string product)
        {
            if (string.Equals(product, ProductType.InHouseCredit.ToString(), StringComparison.OrdinalIgnoreCase) || string.Equals(product, "ihc", StringComparison.OrdinalIgnoreCase))
                return ProductType.InHouseCredit;
            if (string.Equals(product, ProductType.ARAdvance.ToString(), StringComparison.OrdinalIgnoreCase) || string.Equals(product, "arAdvance", StringComparison.OrdinalIgnoreCase))
                return ProductType.ARAdvance;
            return ProductType.LineOfCredit;
        }

        private static Guid[] ParseGuids(IEnumerable<string> strings)
        {
            return strings
                .Select(id => Guid.TryParse(id, out var parsedGuid) ? parsedGuid : (Guid?)null)
                .Where(id => id.HasValue)
                .Select(id => id!.Value)
                .ToArray();
        }
    }
}