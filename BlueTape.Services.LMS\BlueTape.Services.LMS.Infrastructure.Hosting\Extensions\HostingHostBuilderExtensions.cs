using Azure.Identity;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.AzureKeyVault.Services;
using BlueTape.Services.DataAccess.External.DI;
using BlueTape.Services.LMS.Application.DI;
using BlueTape.Services.LMS.DataAccess.DI;
using BlueTape.Services.LMS.DataAccess.SOFR.DI;
using BlueTape.Services.Reporting.DI;
using BlueTape.Services.Utilities.AspNetCore.Tracing;
using BlueTape.Services.Utilities.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Microsoft.Azure.Functions.Worker;

namespace BlueTape.Services.LMS.Infrastructure.Hosting.Extensions;

public static class HostingHostBuilderExtensions
{
    public static IHostBuilder ConfigureBlueTapeHostConfigurationForFunction(this IHostBuilder hostBuilder)
    {
        return hostBuilder.ConfigureHostConfiguration(configuration =>
        {
            var keyVaultUri = new Uri(Environment.GetEnvironmentVariable(InfrastructureConstants.KeyVaultUri)!);
            var azureCredentials = new DefaultAzureCredential();
            configuration.AddAzureKeyVault(keyVaultUri, azureCredentials);
            configuration.SetBasePath(Environment.CurrentDirectory);
            configuration.AddJsonFile("appsettings.json", optional: false);
            configuration.AddJsonFile(
                $"appsettings.{Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment)}.json",
                optional: true);
            configuration.AddEnvironmentVariables();
        });
    }

    public static IHostBuilder ConfigureBlueTapeSerilog(this IHostBuilder hostBuilder, string projectName)
    {
        return hostBuilder.UseSerilog((hostingContext, loggerConfiguration) =>
        {
            loggerConfiguration
                .ReadFrom.Configuration(hostingContext.Configuration)
                .Enrich.FromGlobalLogContext()
                .Enrich.WithProperty(LoggerConstants.ProjectName, projectName)
                .Enrich.WithProperty(LoggerConstants.EnvironmentName, hostingContext.HostingEnvironment.EnvironmentName)
                .Enrich.WithProperty(LoggerConstants.ContentRootPath, hostingContext.HostingEnvironment.ContentRootPath)
                .WriteTo.ApplicationInsights(
                    hostingContext.Configuration.GetSection(LoggerConstants.AppInsightsConnection).Value,
                    TelemetryConverter.Traces);

            loggerConfiguration.WriteTo.Console();
        });
    }

    public static IHostBuilder ConfigureBlueTapeServicesForFunction(this IHostBuilder hostBuilder) =>
        hostBuilder.ConfigureBlueTapeServicesForFunction(c => { });

    public static IHostBuilder ConfigureBlueTapeServicesForFunction(
        this IHostBuilder hostBuilder,
        Action<IServiceCollection> configureDelegate)
    {
        return hostBuilder.ConfigureServices((hostBuilderContext, services) =>
        {
            var configuration = hostBuilderContext.Configuration;
            services.AddOptions();
            services.Configure<BlueTapeOptions>(configuration.GetSection(nameof(BlueTapeOptions)));
            services.AddMemoryCache();
            services.AddScoped<IKeyVaultService, KeyVaultService>();
            services.AddReportingDependencies(configuration);
            services.AddExternalDependencies(configuration);
            services.AddApplicationDependencies(configuration);
            services.AddDataAccessDependencies(configuration);
            services.AddDataAccessSofrDependencies(configuration);
            services.AddBlueTapeTracing();
            services.AddHttpContextAccessor();

            configureDelegate(services);

            var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
            if (!string.IsNullOrEmpty(env))
            {
                var serviceProvider = services.BuildServiceProvider();
                var keyVaultService = serviceProvider.GetRequiredService<IKeyVaultService>();
                var appInsightsConnectionString =
                    keyVaultService.GetSecret(LoggerConstants.AppInsightsConnection).GetAwaiter().GetResult();
                services.AddApplicationInsightsTelemetryWorkerService(x =>
                    x.ConnectionString = appInsightsConnectionString);
                services.ConfigureFunctionsApplicationInsights();
            }

            services.Configure<LoggerFilterOptions>(options =>
            {
                var toRemove = options.Rules.FirstOrDefault(rule => rule.ProviderName
                                                                    ==
                                                                    "Microsoft.Extensions.Logging.ApplicationInsights.ApplicationInsightsLoggerProvider");

                if (toRemove is not null)
                {
                    options.Rules.Remove(toRemove);
                }
            });
        });
    }
}