﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	  <NoWarn>1701;1702;1591;1573</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
	  <NoWarn>1701;1702;1591;1573</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="BlueTape.Services.LMS.IntegrationTests" />
		<InternalsVisibleTo Include="BlueTape.Services.ARS.IntegrationTests" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="12.0.1" />
		<PackageReference Include="AutoMapper.Extensions.EnumMapping" Version="3.1.0" />
		<PackageReference Include="AutoMapper.Extensions.ExpressionMapping" Version="6.0.4" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageReference Include="BlueTape.LS.Domain" Version="1.1.36" />
		<PackageReference Include="BlueTape.LS" Version="1.1.78" />
		<PackageReference Include="DateOnlyTimeOnly.AspNet" Version="2.1.1" />
		<PackageReference Include="DateOnlyTimeOnly.AspNet.Swashbuckle" Version="2.1.1" />
		<PackageReference Include="FluentValidation" Version="11.9.0" />
		<PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Quartz" Version="3.8.0" />
		<PackageReference Include="Quartz.Extensions.DependencyInjection" Version="3.8.0" />
		<PackageReference Include="Quartz.Extensions.Hosting" Version="3.8.0" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
		<PackageReference Include="BlueTape.Utilities" Version="1.4.6" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
		<PackageReference Include="Serilog.Sinks.Seq" Version="6.0.0" />
		<PackageReference Include="bluetape.azurekeyvault" Version="1.0.3" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\BlueTape.Services.ARS.Models\BlueTape.Services.ARS.Models.csproj" />
		<ProjectReference Include="..\BlueTape.Services.ARS\BlueTape.Services.ARS.csproj" />
		<ProjectReference Include="..\BlueTape.Services.LMS.Application\BlueTape.Services.LMS.Application.csproj" />
		<ProjectReference Include="..\BlueTape.Services.LMS.Infrastructure.Hosting\BlueTape.Services.LMS.Infrastructure.Hosting.csproj" />
		<ProjectReference Include="..\BlueTape.Services.LMS.MonitoringService\BlueTape.Services.LMS.MonitoringService.csproj" />
		<ProjectReference Include="..\BlueTape.Services.Reporting\BlueTape.Services.Reporting.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Resources\ViewModels\LoanTemplate\LoanTemplateValidatorResources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>LoanTemplateValidatorResources.resx</DependentUpon>
		</Compile>
		<Compile Update="Resources\ViewModels\Loan\LoanValidatorResources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>LoanValidatorResources.resx</DependentUpon>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Update="Resources\ViewModels\LoanTemplate\LoanTemplateValidatorResources.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
			<LastGenOutput>LoanTemplateValidatorResources.Designer.cs</LastGenOutput>
			<CustomToolNamespace>BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate</CustomToolNamespace>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\ViewModels\Loan\LoanValidatorResources.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
			<LastGenOutput>LoanValidatorResources.Designer.cs</LastGenOutput>
			<CustomToolNamespace>BlueTape.Services.LMS.API.Resources.ViewModels.Loan</CustomToolNamespace>
		</EmbeddedResource>
	</ItemGroup>
</Project>
