﻿using BlueTape.CompanyService.Common.Enums;

namespace BlueTape.Services.LMS.DataAccess.Company.Abstractions.Services;

public interface ICompanyService
{
    Task<List<string>?> GetApplicableCompaniesIds(CancellationToken ct, AccountStatusEnum[]? statuses = null,
        AccountStatusEnum[]? statusesForExclude = null);

    Task ThrowIfCompanyHasBadStandingStatus(string companyId, CancellationToken ct);

    Task<bool> CheckSendFinalPaymentWhenLoanIsPaid(string companyId, string customerAccountId, CancellationToken ct);
}