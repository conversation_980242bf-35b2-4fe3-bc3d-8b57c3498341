﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;

[BsonIgnoreExtraElements]
public class LoanApplicationMetadataDocument
{
    [BsonElement("deductFeeFromFinalPayment")]
    public bool? DeductFeeFromFinalPayment { get; set; }

    [BsonElement("skip_final_payment")]
    public bool? SkipFinalPayment { get; set; }

    [BsonElement("paymentPlan")]
    public LoanPaymentPlanDocument? PaymentPlan { get; set; }

    [BsonElement("loanPackage")]
    public LoanPricingPackageMetadataDocument? LoanPackage { get; set; }
}
