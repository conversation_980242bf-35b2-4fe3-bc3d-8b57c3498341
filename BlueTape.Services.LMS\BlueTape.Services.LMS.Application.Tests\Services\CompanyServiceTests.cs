using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Customers;
using BlueTape.Services.LMS.Application.Tests.Models.Companies;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;
using VariableNullException = BlueTape.Common.ExceptionHandling.Exceptions.VariableNullException;

namespace BlueTape.Services.LMS.Application.Tests.Services;

public class CompanyServiceTests
{
    private readonly DataAccess.Company.Services.CompanyService _companyService;
    private readonly Mock<ICompanyHttpClient> _companyHttpClientMock = new();
    private readonly Mock<ILogger<DataAccess.Company.Services.CompanyService>> _loggerMock = new();
    
    public CompanyServiceTests()
    {
        _companyService = new DataAccess.Company.Services.CompanyService(_companyHttpClientMock.Object, _loggerMock.Object);
    }

    [Fact]
    public Task ThrowIfCompanyHasBadStandingAccountStatus_InvalidCompanyId_ThrowsException()
    {
        CompanyModel company = null;
        _companyHttpClientMock.Setup(x => x.GetCompanyByIdAsync(It.IsAny<string>(), default)).ReturnsAsync(company);

        async Task MethodCallFunc() =>
            await _companyService.ThrowIfCompanyHasBadStandingStatus(It.IsAny<string>(), default);

        return Should.ThrowAsync<VariableNullException>(MethodCallFunc);
    }

    [Theory]
    [ClassData(typeof(BadStandingCompanyModels))]
    public Task ThrowIfCompanyHasBadStandingAccountStatus_BadStandingAccountStatus_ThrowsException(CompanyModel company)
    {
        _companyHttpClientMock.Setup(x => x.GetCompanyByIdAsync(It.IsAny<string>(), default)).ReturnsAsync(company);

        async Task MethodCallFunc() =>
            await _companyService.ThrowIfCompanyHasBadStandingStatus(It.IsAny<string>(), default);

        return Should.ThrowAsync<DeclineDrawException>(MethodCallFunc);
    }

    [Theory]
    [ClassData(typeof(GoodStandingCompanyModels))]
    public async Task ThrowIfCompanyHasBadStandingAccountStatus_ValidData_DoesntThrowException(CompanyModel company)
    {
        _companyHttpClientMock.Setup(x => x.GetCompanyByIdAsync(It.IsAny<string>(), default)).ReturnsAsync(company)
            .Verifiable();

        await _companyService.ThrowIfCompanyHasBadStandingStatus(It.IsAny<string>(), default).ShouldNotThrowAsync();
        _companyHttpClientMock.Verify(x => x.GetCompanyByIdAsync(It.IsAny<string>(), default), Times.Once);
    }

    [Fact]
    public async Task GetApplicableCompaniesIds_WithStatuses_ReturnsCompanyIds()
    {
        // Arrange
        var statuses = new[] { AccountStatusEnum.Active, AccountStatusEnum.Inactive };
        var expectedCompanyIds = new List<string> { "Company1", "Company2" };
        var cancellationToken = CancellationToken.None;

        _companyHttpClientMock
            .Setup(client => client.GetCompanyByStatusesOnlyIds(statuses, cancellationToken))
            .ReturnsAsync(expectedCompanyIds);

        // Act
        var result = await _companyService.GetApplicableCompaniesIds(cancellationToken, statuses);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(expectedCompanyIds);
        _companyHttpClientMock.Verify(client => client.GetCompanyByStatusesOnlyIds(statuses, cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task GetApplicableCompaniesIds_WithExcludedStatuses_ReturnsFilteredCompanyIds()
    {
        // Arrange
        var excludedStatuses = new[] { AccountStatusEnum.Inactive };
        var expectedCompanyIds = new List<string> { "Company1", "Company2" };
        var cancellationToken = CancellationToken.None;
        var filteredStatuses = Enum.GetValues<AccountStatusEnum>().Where(status => !excludedStatuses.Contains(status))
            .ToArray();

        _companyHttpClientMock
            .Setup(client => client.GetCompanyByStatusesOnlyIds(filteredStatuses, cancellationToken))
            .ReturnsAsync(expectedCompanyIds);

        // Act
        var result = await _companyService.GetApplicableCompaniesIds(cancellationToken, null, excludedStatuses);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(expectedCompanyIds);
        _companyHttpClientMock.Verify(client => client.GetCompanyByStatusesOnlyIds(filteredStatuses, cancellationToken),
            Times.Once);
    }

    [Fact]
    public void GetApplicableCompaniesIds_WithEmptyStatuses_ThrowsException()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;

        // Act & Assert
        Should.Throw<VariableNullException>(() =>
        {
            return _companyService.GetApplicableCompaniesIds(cancellationToken, null, null);
        });
    }

    [Fact]
    public async Task CheckSendFinalPaymentWhenLoanIsPaid_WhenCompanyAndCustomerAllow_ReturnsTrue()
    {
        // Arrange
        var companyId = "TestCompanyId";
        var customerAccountId = "TestCustomerAccountId";
        var cancellationToken = CancellationToken.None;

        var company = new CompanyModel
            { Settings = new CompanySettingsModel { SendFinalPaymentWhenLoanIsPaid = true } };
        var customer = new CustomerModel
            { Settings = new CustomerSettingsModel { SendFinalPaymentWhenLoanIsPaid = true } };

        _companyHttpClientMock
            .Setup(client => client.GetCompanyByIdAsync(companyId, cancellationToken))
            .ReturnsAsync(company);

        _companyHttpClientMock
            .Setup(client => client.GetCustomerByIdAsync(customerAccountId, cancellationToken))
            .ReturnsAsync(customer);

        // Act
        var result =
            await _companyService.CheckSendFinalPaymentWhenLoanIsPaid(companyId, customerAccountId, cancellationToken);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task CheckSendFinalPaymentWhenLoanIsPaid_WhenCompanyDoesNotAllow_ReturnsFalse()
    {
        // Arrange
        var companyId = "TestCompanyId";
        var customerAccountId = "TestCustomerAccountId";
        var cancellationToken = CancellationToken.None;

        var company = new CompanyModel
            { Settings = new CompanySettingsModel { SendFinalPaymentWhenLoanIsPaid = false } };

        _companyHttpClientMock
            .Setup(client => client.GetCompanyByIdAsync(companyId, cancellationToken))
            .ReturnsAsync(company);

        // Act
        var result =
            await _companyService.CheckSendFinalPaymentWhenLoanIsPaid(companyId, customerAccountId, cancellationToken);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task CheckSendFinalPaymentWhenLoanIsPaid_WhenCustomerDoesNotAllow_ReturnsFalse()
    {
        // Arrange
        var companyId = "TestCompanyId";
        var customerAccountId = "TestCustomerAccountId";
        var cancellationToken = CancellationToken.None;

        var company = new CompanyModel
            { Settings = new CompanySettingsModel { SendFinalPaymentWhenLoanIsPaid = true } };
        var customer = new CustomerModel
            { Settings = new CustomerSettingsModel { SendFinalPaymentWhenLoanIsPaid = false } };

        _companyHttpClientMock
            .Setup(client => client.GetCompanyByIdAsync(companyId, cancellationToken))
            .ReturnsAsync(company);

        _companyHttpClientMock
            .Setup(client => client.GetCustomerByIdAsync(customerAccountId, cancellationToken))
            .ReturnsAsync(customer);

        // Act
        var result =
            await _companyService.CheckSendFinalPaymentWhenLoanIsPaid(companyId, customerAccountId, cancellationToken);

        // Assert
        result.ShouldBeFalse();
    }
}