﻿using BlueTape.CompanyClient.DI;
using BlueTape.InvoiceClient.DI;
using BlueTape.OBS.Client.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Services.DataAccess.External.DI;

public static class DependencyRegistrar
{
    public static void AddExternalDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddCompanyServiceClient(configuration);
        services.AddInvoiceServiceClient(configuration);
        services.AddDataAccessObsDependencies(configuration);
    }
}