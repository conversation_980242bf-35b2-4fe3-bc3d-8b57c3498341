﻿using Amazon;
using Amazon.Runtime;
using Amazon.SimpleNotificationService;
using BlueTape.DataAccess.Mongo.DI;
using BlueTape.DraftsMapper.Extensions;
using BlueTape.Notification.Sender.DI;
using BlueTape.Services.LMS.Application.Abstractions.Calculators;
using BlueTape.Services.LMS.Application.Abstractions.Senders;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Abstractions.Services.CreditServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.OverDueServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.PaymentServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.PenaltyServices;
using BlueTape.Services.LMS.Application.Abstractions.Validators;
using BlueTape.Services.LMS.Application.Abstractions.Validators.Validators.Base;
using BlueTape.Services.LMS.Application.Calculators;
using BlueTape.Services.LMS.Application.Calculators.LoanDetails;
using BlueTape.Services.LMS.Application.Calculators.LoanDetails.Interfaces;
using BlueTape.Services.LMS.Application.Calculators.LoanDetails.Models;
using BlueTape.Services.LMS.Application.Comparisons;
using BlueTape.Services.LMS.Application.Infrastructure.ChangeFeeReceivableStrategy;
using BlueTape.Services.LMS.Application.Infrastructure.ChangeFeeReceivableStrategy.Abstractions;
using BlueTape.Services.LMS.Application.Infrastructure.ChangeLoanStatusStrategy;
using BlueTape.Services.LMS.Application.Infrastructure.ChangeLoanStatusStrategy.Abstractions;
using BlueTape.Services.LMS.Application.Infrastructure.ChangePaymentStatusStrategy;
using BlueTape.Services.LMS.Application.Infrastructure.ChangePaymentStatusStrategy.Abstractions;
using BlueTape.Services.LMS.Application.Mappers;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Application.Senders;
using BlueTape.Services.LMS.Application.Services;
using BlueTape.Services.LMS.Application.Services.CreditServices;
using BlueTape.Services.LMS.Application.Services.LoanServices;
using BlueTape.Services.LMS.Application.Services.OverDueServices;
using BlueTape.Services.LMS.Application.Services.PaymentServices;
using BlueTape.Services.LMS.Application.Services.PenaltyServices;
using BlueTape.Services.LMS.Application.Validators;
using BlueTape.Services.LMS.DataAccess.Company.Abstractions.Services;
using BlueTape.Services.LMS.DataAccess.Company.DI;
using BlueTape.Services.LMS.DataAccess.DI;
using BlueTape.Services.LMS.DataAccess.SOFR.DI;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Services.LMS.Infrastructure.Options.PenaltyInterestOptions;
using BlueTape.Services.LMS.UnitOfWork.DI;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Services;
using BlueTape.SNS.SlackNotification.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;
using System.Reflection;

namespace BlueTape.Services.LMS.Application.DI
{
    public static class DependencyRegistrar
    {
        public static void AddApplicationDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddAutoMapper(typeof(ModelsProfile).GetTypeInfo().Assembly);

            services.AddTransient<ILoanReceivableService, LoanReceivableService>();
            services.AddTransient<ILoanTemplateService, LoanTemplateService>();
            services.AddTransient<ILoanService, LoanService>();
            services.AddTransient<IAutoPayLoanService, AutoPayLoanService>();
            services.AddTransient<IAuxiliaryLoanService, AuxiliaryLoanService>();
            services.AddTransient<ILoanDownPaymentsService, LoanDownPaymentsService>();
            services.AddTransient<ICalculatorService, CalculatorService>();
            services.AddTransient<ILoanCalculatorBase<LoanDetailsCalculation>, LoanDetailsCalculator>();
            services.AddTransient<ILoanCalculatorBase<LoanPayablesDetailsCalculation>, LoanPayablesDetailsCalculator>();
            services.AddTransient<ILoanPayablesDetailsService, LoanPayablesDetailsService>();
            services.AddTransient<ILoanDetailsService, LoanDetailsService>();
            services.AddTransient<ICreditDetailsService, CreditDetailsService>();
            services.AddTransient<IReceivableTypeAmountCalculator, ReceivableTypeAmountCalculator>();

            services.AddTransient<IInvoiceSyncMessageSender, InvoiceSyncMessageSender>();

            services.AddTransient<IOverDueReceivablesService, OverDueReceivablesService>();
            services.AddTransient<IPenaltyInterestReceivableService, PenaltyInterestReceivableService>();
            services.AddTransient<IPenaltyDetectorService, PenaltyDetectorService>();
            services.AddTransient<IPenaltyInterestCalculator, PenaltyInterestCalculator>();
            services.AddTransient<IParametersCalculationService, ParametersCalculationService>();

            services.AddTransient<IValidator<Loan>, LoanValidator>();
            services.AddTransient<IOverDueDetectorService, OverDueDetectorService>();
            services.AddTransient<IPaymentService, Services.PaymentServices.PaymentService>();
            services.AddTransient<IPaymentProcessService, PaymentProcessService>();
            services.AddTransient<IProcessingAmountService, ProcessingAmountService>();
            services.AddTransient<ILoanParametersService, LoanParametersService>();
            services.AddTransient<ILoanReceivableService, LoanReceivableService>();
            services.AddTransient<IApplyReceivablesService, ApplyReceivablesService>();
            services.AddTransient<ILateFeeReceivableService, LateFeeReceivableService>();
            services.AddTransient<ICreateLoanService, CreateLoanService>();
            services.AddTransient<ILoanReplanService, LoanReplanService>();
            services.AddTransient<ICancelLateFeeReceivableService, CancelLateFeeReceivableService>();
            services.AddTransient<IPenaltyInterestStopRulesEngine, PenaltyInterestStopRulesEngine>();
            services.AddTransient<IPenaltyExclusionsService, PenaltyExclusionsService>();
            services.AddTransient<ISecuredRatesService, SecuredRatesService>();
            services.AddTransient<ICreditService, CreditService>();
            services.AddTransient<ICompanyService, DataAccess.Company.Services.CompanyService>();
            services.AddTransient<IAuthorizationPeriodService, AuthorizationPeriodService>();
            services.AddTransient<ICreditHoldService, CreditHoldService>();
            services.AddTransient<ICreditStatusHistoryService, CreditStatusHistoryService>();
            services.AddTransient<ILoanReceivablePaymentTimelineService, LoanReceivablePaymentTimelineService>();

            services.AddSingleton<IDateCalculator, DateCalculator>();
            services.AddSingleton<IFeeCalculator, FeeCalculator>();
            services.AddSingleton<ILoanReceivableCalculator, LoanReceivableCalculator>();
            services.AddSingleton<ILateFeeCalculator, LateFeeCalculator>();

            services.AddScoped<IReceivableRecalculatorService, ReceivableRecalculatorService>();
            services.AddScoped<IPaymentEligibilityChecker, PaymentEligibilityChecker>();

            services.AddTransient<IChangePaymentStatusStrategy, ApprovePaymentStrategy>();
            services.AddTransient<IChangePaymentStatusStrategy, RejectPaymentStrategy>();
            services.AddTransient<IChangePaymentStatusStrategy, CancelPaymentStrategy>();

            services.AddTransient<IChangeFeeReceivableStrategy, ChangeLateFeeReceivableStrategy>();
            services.AddTransient<IChangeFeeReceivableStrategy, ChangeManualFeeReceivableStrategy>();

            services.AddTransient<IChangeLoanStatusStrategy, DefaultChangeStatusStrategy>();
            services.AddTransient<IChangeLoanStatusStrategy, CloseLoanStrategy>();
            services.AddTransient<IChangeLoanStatusStrategy, CancelLoanStrategy>();
            services.AddTransient<IChangeLoanStatusStrategy, StartLoanStrategy>();
            services.AddTransient<IChangeLoanStatusStrategy, NoneLoanStrategy>();
            services.AddTransient<IChangeLoanStatusStrategy, DefaultedLoanStrategy>();
            services.AddTransient<IChangeLoanStatusStrategy, RefinanceLoanStrategy>();
            services.AddTransient<IChangeLoanStatusStrategy, RecoverLoanStrategy>();

            services.AddTransient<IManualPaymentService, ManualPaymentService>();
            services.AddTransient<IChangeLogService, ChangeLogService>();

            services.AddTransient<IComparer<LoanReceivableEntity>, ReceivableTypeComparer>();
            services.AddTransient<ILoanReplanService, LoanReplanService>();
            services.AddTransient<IReceivablesRescheduleService, ReceivablesRescheduleService>();
            services.AddTransient<ILoanReschedulingValidator, LoanReschedulingValidator>();
            services.AddTransient<ILoanReplanValidator, LoanReplanValidator>();

            services.AddTransient<ICreditMigrationService, CreditMigrationService>();

            services.AddTransient<IRefundsProcessService, RefundsProcessService>();
            services.AddTransient<IBasisPointService, BasisPointService>();

            services.AddTransient<ILoanPricingPackageService, LoanPricingPackageService>();

            services.AddTransient<ICardPricingPackageService, CardPricingPackageService>();

            services.AddTransient<ISlackNotificationService, SlackNotificationService>();
            services.AddSnsSlackNotifications();
            services.AddDefaultAWSOptions(config.GetAWSOptions());
            services.AddSingleton<IAmazonSimpleNotificationService>(sp =>
            {
                var awsCredentials = new BasicAWSCredentials(config["AWS-ACCESS-KEY-ID"], config["AWS-SECRET-ACCESS-KEY"]);
                var region = RegionEndpoint.GetBySystemName(config["AWS-DEFAULT-REGION"]);
                return new AmazonSimpleNotificationServiceClient(awsCredentials, region);
            });

            services.AddDataAccessDependencies(config);
            services.AddMongoDataAccessDependencies(config);
            services.AddBusinessLogicDependencies(config);
            services.AddDataAccessSofrDependencies(config);
            services.AddUnitOfWork(config);
            services.AddCompanyServiceDependencies(config);
            services.AddAzureSystemNotifications();

            services.AddTransient<ICreditStatusDetectorService, CreditStatusDetectorService>();
            services.Configure<RefundOptions>(config.GetSection(nameof(RefundOptions)));
            services.Configure<ReplanOptions>(config.GetSection(nameof(ReplanOptions)));
            services.Configure<S3ConfigurationOptions>(config.GetSection(nameof(S3ConfigurationOptions)));
            services.Configure<SlackNotificationOptions>(config.GetSection(SlackNotificationOptions.SectionName));
            services.Configure<SyncOptions>(config.GetSection(nameof(SyncOptions)));
            services.Configure<EmailTemplateOptions>(config.GetSection(nameof(EmailTemplateOptions)));
            services.Configure<HttpInteractionOptions>(config.GetSection(nameof(HttpInteractionOptions)));
            services.Configure<PenaltyInterestTriggerOptions>(config.GetSection(nameof(PenaltyInterestTriggerOptions)));
        }
    }
}
