﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="BlueTape.Common.FileService" Version="1.0.6" />
	<PackageReference Include="BlueTape.CompanyClient" Version="1.0.46" />
    <PackageReference Include="bluetape.azurekeyvault" Version="1.0.3" />
    <PackageReference Include="bluetape.awss3" Version="1.1.5" />
    <PackageReference Include="BlueTape.CompanyService" Version="1.2.40" />
	<PackageReference Include="bluetape.emailsender" Version="3.0.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlueTape.DataAccess.Mongo\BlueTape.DataAccess.Mongo.csproj" />
    <ProjectReference Include="..\BlueTape.OBS.Client\BlueTape.OBS.Client.csproj" />
    <ProjectReference Include="..\BlueTape.Services.DataAccess.External\BlueTape.Services.DataAccess.External.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.DataAccess.Company\BlueTape.Services.LMS.DataAccess.Company.csproj" />
    <ProjectReference Include="..\BlueTape.Services.Reporting.DataAccess\BlueTape.Services.Reporting.DataAccess.csproj" />
    <ProjectReference Include="..\BlueTape.Services.Reporting.Domain\BlueTape.Services.Reporting.Domain.csproj" />
  </ItemGroup>

</Project>
