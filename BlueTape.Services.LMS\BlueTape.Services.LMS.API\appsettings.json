{"Quartz": {"MissedPaymentsJob": "0 0 0 * * ?", "MissingTabapayReportFilesJob": "0 50 23 * * ?", "ProcessingPaymentsForALongTimeJob": "0 0 20 * * ?", "TabapayProcessingCardPaymentForALongTimeJob": "0 0 21 * * ?"}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=penaltyPaymentTest;Username=postgres;Password=********"}, "Serilog": {"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.DataProtection": "Error", "Microsoft.EntityFrameworkCore": "Information", "System.Net.Http.HttpClient": "Information", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}}}, "Seq": {"Address": "http://localhost:5341"}}, "RefundOptions": {"RefundConvenienceDelayInDays": "30", "MinimumRefundSeparationAmount": "0.00"}, "ReplanOptions": {"ReplanConvenienceDelayInDays": "30"}, "SyncOptions": {"LastSyncDateThresholdDifferenceInSeconds": "3"}, "S3ConfigurationOptions": {"S3BucketName": "uw1.loan-tape-aion", "DWHAssetsS3BucketName": "dwh-assets", "ArcadiaS3BucketName": "arcadia-bluetape-staging", "ArcadiaAwsRegion": "us-east-1", "BlueTapeDefaultAwsRegion": "us-west-1", "ArcadiaS3BucketAccessKey": "ARCADIA-S3-BUCKET-ACCESS-KEY", "ArcadiaS3BucketSecretKey": "ARCADIA-S3-BUCKET-SECRET-KEY"}, "EmailTemplateOptions": {"ArcadiaLoanTapeReportEmailKey": "LOAN-TAPE-REPORT-EMAIL-ARCADIA", "RaistoneLoanTapeReportEmailKey": "LOAN-TAPE-REPORT-EMAIL-RAISTONE", "LoanTapeReportEmailTemplateId": "d-a9fb3adaa54543bc9d5e09c4616d41ef"}, "LoanTapeReportOptions": {"FinalPaymentDaysBefore": 3, "FinalPaymentDaysAfter": 3}, "InvoiceDrawsRejectReportOptions": {"EnableEmailNotification": false}, "AccountApplicationsRejectReportOptions": {"RejectionReasons": {"L01": "Business is too recent", "L02": "Revenue is too low", "L03": "Business cash flow positions insufficient", "L04": "Unwilling to link bank account", "L05": "Unable to verify the business", "L06": "Unable to KYC co-owners", "L07": "One or more co-owners have bankruptcy filings", "L08": "One or more co-owners has insufficient credit (Fico<630)", "L09": "Business bankruptcy in last 24 months", "L10": "Any lien in the past 12 months > Max($5k, 1.5% of rev.)", "L11": "Business was declined by BT in the last 6 months", "L12": "Business reported trade lines too recent", "L13": "Number of past due payments by 60d or more is too high", "L14": "Debt to income above 15", "L15": "Fails Trade Reference", "L16": "<PERSON><PERSON>"}, "EnableEmailNotification": false}, "SlackNotification": {"SnsTopicName": "loantape-report-notifications-dev", "MonitoringSnsTopicName": "monitoring-notifications-dev"}, "HttpInteractionOptions": {"LoanManagementServiceApiKey": "NET-LMS-APIKEY", "SofrServiceApiKey": "NET-SOFR-API-KEY"}, "PenaltyInterestTriggerOptions": {"MinimumAvailablePenaltyInterestAmount": 1, "triggerRules": [{"name": "TwoTimesThreeDaysOrOneTimeSevenDaysRule", "triggeringItems": [{"MissedPaymentsCount": 2, "MissedPaymentsMinimumLateDays": 3}, {"MissedPaymentsCount": 1, "MissedPaymentsMinimumLateDays": 7}]}]}, "AgingReportOptions": {"AgingReportItems": [{"type": "PENDING"}, {"type": "DUE"}, {"type": "PROCESSING"}, {"type": "PASTDUE", "fromDate": 1, "toDate": 30}, {"type": "PASTDUE", "fromDate": 31, "toDate": 60}, {"type": "PASTDUE", "fromDate": 61, "toDate": 90}, {"type": "PASTDUE", "fromDate": 91, "toDate": 120}, {"type": "PASTDUE", "fromDate": 121, "toDate": 150}, {"type": "PASTDUE", "fromDate": 151, "toDate": 180}, {"type": "PASTDUEPLUS", "fromDate": 180}]}, "MonitoringServiceOptions": {"ProcessingPaymentsDaysThreshold": 7, "TabapayProcessingCardPaymentDaysThreshold": 7}, "CashFlowBuilderOptions": {"ExecutionDelay": "00:00:00.1500000"}, "AllowedHosts": "*", "API_KEY": "123", "NET-SOFR-API-URL": "https://localhost:7187", "NET-COMPANIES-APIKEY": "123", "MINIMUM_DUE_AMOUNT": 500, "AION-ELIGIBLE-COMPANIES": "all"}