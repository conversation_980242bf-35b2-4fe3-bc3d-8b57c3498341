﻿using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Extensions;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Utilities.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace BlueTape.Services.LMS.Application.Services.LoanServices;
public class AutoPayLoanService(ILoanService loanService) : IAutoPayLoanService
{
    public async Task<IEnumerable<AutoPayLoan>?> GetUpcoming(DateOnly? fromDate, int countDays, CancellationToken ct)
    {
        IEnumerable<Loan>? loans;
        if (fromDate.HasValue)
        {
            var date = fromDate.Value;

            loans = await loanService.GetUpcomingAndOverdueLoans(date.AddDays(countDays), null, ct);
        }
        else loans = await loanService.Get(ct);

        if (loans is null)
        {
            return new List<AutoPayLoan>();
        }

        var autoPayLoans = loans.Select(GetAutoPayLoan).Where(autoPay => autoPay.NextPaymentAmount > 0);

        return autoPayLoans;
    }

    private static AutoPayLoan GetAutoPayLoan(Loan loan)
    {
        var processingAmount = loan.Payments.Where(p => p.Status is PaymentStatus.Processing
                               && p.SubType is PaymentSubType.Repayment).Sum(p => p.Amount);

        var nextPaymentDate = GetNextPaymentDate(loan.LoanReceivables);
        return new AutoPayLoan()
        {
            Id = loan.Id,
            IsOverdue = loan.IsOverdue,
            DueStatus = GetLoanDueStatus(loan.LoanReceivables, processingAmount),
            OverDueAmount = GetOverDueAmount(loan.LoanReceivables, processingAmount, loan.IsOverdue),
            NextPaymentAmount = GetNextPaymentAmount(loan.LoanReceivables, processingAmount, nextPaymentDate),
            NextPaymentDate = nextPaymentDate,
            NextPaymentDetails = GetNextPaymentDetails(loan.LoanReceivables, nextPaymentDate)
        };
    }

    private static LoanDueStatus GetLoanDueStatus(IEnumerable<LoanReceivable> receivables, decimal processingAmount)
    {
        var currentDate = DateOnly.FromDateTime(DateTime.UtcNow);
        var loanReceivableEntities = receivables.ToList();

        var pastNotPaidReceivable =
            loanReceivableEntities.Find(r => r.IsActiveAndNotPaid() && r.ExpectedDate <= currentDate);
        var futureNotPaidReceivable =
            loanReceivableEntities.Find(r => r.IsActiveAndNotPaid() && r.ExpectedDate >= currentDate);

        if (pastNotPaidReceivable is null) return futureNotPaidReceivable is null
            ? LoanDueStatus.Paid
            : LoanDueStatus.Pending;
        if (IsReceivableOverdue(currentDate, pastNotPaidReceivable)) return LoanDueStatus.Overdue;
        if (IsReceivablePastDue(currentDate, pastNotPaidReceivable)) return processingAmount == 0
            ? LoanDueStatus.PastDue
            : LoanDueStatus.PastDuePaymentProcessing;

        return LoanDueStatus.Due;
    }

    private static List<NextPaymentDetails> GetNextPaymentDetails(IEnumerable<LoanReceivable> receivables, DateOnly date)
    {
        var listOfNextPaymentDetails = new List<NextPaymentDetails>();
        var receivablesOnDate = receivables
            .Where(r => r.ExpectedDate == date)
            .ToList();

        foreach (var receivableOnDate in receivablesOnDate)
        {
            var sameTypesCount = receivablesOnDate.Count(r => r.Type == receivableOnDate.Type);

            if (sameTypesCount > 1)
            {
                if (IsListOfNextPaymentDetailsHaveSameType(listOfNextPaymentDetails, receivableOnDate)) continue;

                var nextPaymentDetailsWithSameTypes = new NextPaymentDetails()
                {
                    ReceivableType = receivableOnDate.Type,
                    Amount = GetOutstandingAmountFromTheSameReceivableTypes(receivablesOnDate, receivableOnDate),
                    Count = sameTypesCount,
                };

                listOfNextPaymentDetails.Add(nextPaymentDetailsWithSameTypes);
                continue;
            }

            var nextPaymentDetails = new NextPaymentDetails()
            {
                ReceivableType = receivableOnDate.Type,
                Amount = GetOutstandingAmount(receivableOnDate),
                Count = 1,
            };

            listOfNextPaymentDetails.Add(nextPaymentDetails);
        }

        return listOfNextPaymentDetails;
    }

    private static decimal GetOverDueAmount(IEnumerable<LoanReceivable> receivables, decimal processingAmount, bool isOverdue)
    {
        if (!isOverdue) return 0;

        decimal overDueAmount = 0;

        foreach (var receivable in receivables)
        {
            if (receivable.ExpectedDate < DateOnly.FromDateTime(DateTime.UtcNow) &&
                receivable.IsActiveAndNotPaid())
            {
                overDueAmount = overDueAmount - receivable.PaidAmount + receivable.AdjustAmount + receivable.ExpectedAmount;
            }
        }

        var overdueAmountWithoutProcessingAmount = overDueAmount - processingAmount;
        return overdueAmountWithoutProcessingAmount > 0 ? overdueAmountWithoutProcessingAmount : 0;
    }

    private static decimal GetNextPaymentAmount(IEnumerable<LoanReceivable> receivables, decimal processingAmount, DateOnly date)
    {
        decimal amount = 0;

        foreach (var receivable in receivables)
        {
            if (receivable.ExpectedDate <= date && receivable.IsActiveAndNotPaid())
            {
                amount = amount - receivable.PaidAmount + receivable.AdjustAmount + receivable.ExpectedAmount;
            }
        }
        return amount - processingAmount;
    }

    private static DateOnly GetNextPaymentDate(IEnumerable<LoanReceivable> receivables)
    {
        var loanReceivableEntities = receivables.ToList();
        var nextReceivable = loanReceivableEntities.Find(x =>
            x.ExpectedDate >= DateOnly.FromDateTime(DateTime.UtcNow) && x.IsActiveAndNotPaid());

        return nextReceivable?.ExpectedDate ?? loanReceivableEntities[^1].ExpectedDate;
    }

    private static decimal GetOutstandingAmount(LoanReceivable loanReceivable)
    {
        return loanReceivable.ExpectedAmount - loanReceivable.PaidAmount + loanReceivable.AdjustAmount;
    }

    private static decimal GetOutstandingAmountFromTheSameReceivableTypes(IEnumerable<LoanReceivable> loanReceivables, LoanReceivable loanReceivable)
    {
        return loanReceivables
            .Where(r => r.Type == loanReceivable.Type)
            .Sum(r => r.ExpectedAmount - r.PaidAmount + r.AdjustAmount);
    }

    private static bool IsListOfNextPaymentDetailsHaveSameType(List<NextPaymentDetails> listOfNextPaymentDetails,
        LoanReceivable loanReceivable)
    {
        return listOfNextPaymentDetails.Exists(r => r.ReceivableType == loanReceivable.Type);
    }

    private static bool IsReceivableOverdue(DateOnly currentDate, LoanReceivable receivable)
    {
        return currentDate > receivable.ExpectedDate.AddBusinessDays(3);
    }

    private static bool IsReceivablePastDue(DateOnly currentDate, LoanReceivable receivable)
    {
        return currentDate > receivable.ExpectedDate;
    }
}
