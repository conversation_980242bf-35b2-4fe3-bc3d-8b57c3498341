variable "environment" {
  type        = string
}

variable "location" {
  type        = string
}

variable "resource_group_name" {
  type        = string
}

variable "client_id" {
  description = "ARM client id"
}

variable "tenant_id" {
  description = "ARM Tenant id"
}

variable "client_secret" {
  description = "ARM client secret"
}

variable "app_insights_instrumentation_key" {
    type        = string
}

variable "app_insights_connection_string" {
    type        = string
}

variable "key_vault_uri" {
    type        = string
}

variable "storage_account_name" {
    type        = string
}

variable "storage_account_access_key" {
    type        = string
}

variable "service_plan_core_id" {
    type        = string
}

variable "user_assigned_identity_core_id" {
    type        = string
}

variable "dotnet_version" {
    type        = string
}

variable "functions_worker_runtime" {
    type        = string
}

variable "function_extension_version" {
    type        = string
}

variable "storage_account_primary_connection_string" {
    type        = string
}

variable "application_name" {
    type        = map(any)
}

variable "virtual_network_subnet_id" {
  type          = string
}

variable "aws_access_key_id" {
  description = "AWS access key id"
  default = ""
}

variable "aws_secret_access_key" {
  description = "AWS secret access key"
  default = ""
}

variable "credit_status_queue_name" {
  type        = string
}

variable "credit_status_queue_connection" {
  type        = string
}

variable "aws_default_region" {
  description = "AWS default region"
  default = "us-west-1"
}

variable "lp_aws_account" {
  description = "AWS account id"
  default = ""
}