﻿using AutoMapper;
using BlueTape.OBS.Enums;
using BlueTape.Services.LMS.Application.Abstractions.Senders;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.PaymentServices;
using BlueTape.Services.LMS.Application.Infrastructure.ChangeLoanStatusStrategy.Abstractions;
using BlueTape.Services.LMS.Application.Mappers;
using BlueTape.Services.LMS.Application.Models.ChangeLog;
using BlueTape.Services.LMS.Application.Models.Filters;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Application.Services.LoanServices;
using BlueTape.Services.LMS.Application.Tests.Attributes;
using BlueTape.Services.LMS.Application.Tests.Entities.Loans;
using BlueTape.Services.LMS.Application.Tests.Entities.LoanTemplates;
using BlueTape.Services.LMS.Application.Tests.Models.Loans;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using TinyHelpers.Extensions;
using Xunit;
using RepositoryLoansByEinHashAndStatusFilter = BlueTape.Services.LMS.Domain.Entities.Filters.LoansByEinHashAndStatusFilter;

namespace BlueTape.Services.LMS.Application.Tests.Services
{
    public class LoanServiceTests
    {
        private readonly LoanService _loanService;
        private readonly LoanService _loanServiceWithMapper;
        private readonly Mock<ILoanRepository> _loanRepoMock = new();
        private readonly Mock<IProcessingAmountService> _processingServiceMock = new();
        private readonly Mock<ILoanReceivableService> _loanReceivableServiceMock = new();
        private readonly Mock<ILoanTemplateRepository> _loanTemplateRepositoryMock = new();
        private readonly Mock<IChangeLoanStatusStrategy> _changeLoanStatusStrategyMock = new();
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Mock<IChangeLogService> _changeLogMock = new();
        private readonly Mock<ILogger<LoanService>> _loggerMock = new();
        private readonly Mock<ICreateLoanService> _createLoanServiceMock = new();
        private readonly Mock<IInvoiceSyncMessageSender> _invoiceSyncMessageSender = new();
        private readonly Mock<IPaymentProcessTemplateRepository> _paymentProcessTemplateRepository = new();
        private readonly Mock<IPaymentProcessTemplateHistoryRepository> _paymentProcessTemplateHistoryRepository = new();
        private readonly Mock<ILoanDetailsService> _loanDetailsService = new();

        public LoanServiceTests()
        {
            var changeLoanStatusStrategies = new List<IChangeLoanStatusStrategy>()
            {
                _changeLoanStatusStrategyMock.Object,
            };

            _loanService = new LoanService(_loanRepoMock.Object,
                _paymentProcessTemplateRepository.Object,
                changeLoanStatusStrategies,
                _mapperMock.Object,
                _loanReceivableServiceMock.Object,
                _loanTemplateRepositoryMock.Object,
                _processingServiceMock.Object,
                _changeLogMock.Object,
                _loggerMock.Object,
                _createLoanServiceMock.Object,
                _loanDetailsService.Object,
                _paymentProcessTemplateHistoryRepository.Object);

            MapperConfiguration mapperConfig = new MapperConfiguration(
                cfg => { cfg.AddProfile(new ModelsProfile()); });

            _loanServiceWithMapper = new LoanService(_loanRepoMock.Object,
                _paymentProcessTemplateRepository.Object,
                changeLoanStatusStrategies,
                new Mapper(mapperConfig),
                _loanReceivableServiceMock.Object,
                _loanTemplateRepositoryMock.Object,
                _processingServiceMock.Object,
                _changeLogMock.Object,
                _loggerMock.Object,
                _createLoanServiceMock.Object,
                _loanDetailsService.Object,
                _paymentProcessTemplateHistoryRepository.Object);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetLoans_HasData_ReturnLoansList(IEnumerable<LoanEntity> entities, IEnumerable<Loan> models)
        {
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(It.IsAny<IEnumerable<LoanEntity>>())).Returns(models);
            _loanRepoMock.Setup(x => x.Get(default)).ReturnsAsync(entities);

            var result = await _loanService.Get(default);

            result.ShouldBeEquivalentTo(models);
        }

        [Fact]
        public async Task GetLoansWithPredicate_HasData_ReturnLoansList()
        {
            var notOverdueLoan = new Loan() { IsOverdue = false };
            var notOverdueLoanEntity = new LoanEntity() { IsOverdue = false };

            var listWithLoans = new List<Loan>() { notOverdueLoan };
            var listWithLoanEntities = new List<LoanEntity>() { notOverdueLoanEntity };

            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(It.IsAny<IEnumerable<LoanEntity>>()))
                .Returns(listWithLoans);
            _loanRepoMock.Setup(x => x.Get(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default))
                .ReturnsAsync(listWithLoanEntities);
            var result = await _loanService.Get(x => x.IsOverdue == false, default);

            result.ShouldBeEquivalentTo(listWithLoans);
        }

        [Fact]
        public async Task GetLoans_HasNotData_ReturnEmptyLoansList()
        {
            var models = InvalidLoanModels.EmptyLoanList;
            var entities = InvalidLoanEntities.EmptyLoanEntitiesList;

            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(It.IsAny<IEnumerable<LoanEntity>>())).Returns(models);
            _loanRepoMock.Setup(x => x.Get(default)).ReturnsAsync(entities);

            var result = await _loanService.Get(default);

            result.ShouldBeEquivalentTo(models);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetLoanById_ValidId_ReturnsLoanById(LoanEntity entity, Loan model)
        {
            _mapperMock.Setup(x => x.Map<LoanEntity>(It.IsAny<Loan>())).Returns(entity);
            _loanRepoMock.Setup(x => x.GetById(It.IsAny<Guid>(), default)).ReturnsAsync(entity);
            _mapperMock.Setup(x => x.Map<Loan>(It.IsAny<LoanEntity>())).Returns(model);

            var result = await _loanService.GetById(model.Id, default);

            result.ShouldBeEquivalentTo(model);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetLoanByIdsList_ValidIds_ReturnsLoans(List<LoanEntity> entities, List<Loan> loans,
            List<Guid> ids)
        {
            _mapperMock.Setup(x => x.Map<IEnumerable<LoanEntity>>(It.IsAny<IEnumerable<Loan>>())).Returns(entities);
            _loanRepoMock.Setup(x => x.GetByIds(It.IsAny<IEnumerable<Guid>>(), default)).ReturnsAsync(entities);
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(It.IsAny<IEnumerable<LoanEntity>>())).Returns(loans);

            var result = await _loanService.GetByIds(ids, false, default);

            _loanRepoMock.Verify(x => x.GetByIds(It.IsAny<IEnumerable<Guid>>(), default), Times.Once());
            result.ShouldBeEquivalentTo(loans);
        }

        [Fact]
        public Task GetLoanById_InvalidId_ThrowException()
        {
            var entity = InvalidLoanEntities.NullLoanEntity;
            var model = InvalidLoanModels.NullLoan;

            _loanRepoMock.Setup(x => x.GetById(It.IsAny<Guid>(), default)).ReturnsAsync(entity);
            _mapperMock.Setup(x => x.Map<Loan>(It.IsAny<LoanEntity>())).Returns(model);

            return Assert.ThrowsAsync<VariableNullException>(() => _loanService.GetById(Guid.NewGuid(), null, default));
        }

        [Theory, AutoDataWithDateOnly]
        public void GetLoansByPredicate_ValidPredicate_ReturnsLoansList(IEnumerable<Loan> models,
            Expression<Func<Loan, bool>> predicateModel, Expression<Func<LoanEntity, bool>> predicateEntities)
        {
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(It.IsAny<IEnumerable<LoanEntity>>())).Returns(models);
            _mapperMock.Setup(x => x.Map<Expression<Func<LoanEntity, bool>>>(It.IsAny<Expression<Func<Loan, bool>>>()))
                .Returns(predicateEntities);
            var result = _loanService.Get(predicateModel, default);

            result.ShouldNotBeNull();
        }


        [Theory, AutoDataWithDateOnly]
        public Task DeleteLoan_ValidId_ReturnsNull(Guid id)
        {
            _loanRepoMock.Setup(x => x.Delete(It.IsAny<Guid>(), default));

            return _loanService.Delete(id, default).ShouldNotThrowAsync();
        }

        [Theory, AutoDataWithDateOnly]
        public Task DeleteLoan_ValidLoan_ReturnsNull(LoanEntity entity, Loan model)
        {
            _mapperMock.Setup(x => x.Map<LoanEntity>(It.IsAny<Loan>())).Returns(entity);
            _loanRepoMock.Setup(x => x.Delete(entity, default));

            return _loanService.Delete(model, default).ShouldNotThrowAsync();
        }

        [Theory, AutoDataWithDateOnly]
        public async Task UpdateLoan_ValidLoan_ReturnsLoan(LoanEntity entity, Loan model)
        {
            _mapperMock.Setup(x => x.Map<LoanEntity>(It.IsAny<Loan>())).Returns(entity);
            _loanRepoMock.Setup(x => x.Update(entity, default, default)).ReturnsAsync(() => entity);
            _mapperMock.Setup(x => x.Map<Loan>(It.IsAny<LoanEntity>())).Returns(model);

            var result = await _loanService.Update(model, default);

            result.ShouldBeEquivalentTo(model);
        }

        [Fact]
        public async Task UpdateLoan_InvalidLoan_ReturnsNull()
        {
            var entity = InvalidLoanEntities.NullLoanEntity;
            var model = InvalidLoanModels.NullLoan;

            _mapperMock.Setup(x => x.Map<LoanEntity>(It.IsAny<Loan>())).Returns(entity);
            _loanRepoMock.Setup(x => x.Update(It.IsAny<LoanEntity>(), default, default)).ReturnsAsync(entity);
            _mapperMock.Setup(x => x.Map<Loan>(It.IsAny<LoanEntity>())).Returns(model);
            var result = await _loanService.Update(model, default);

            result.ShouldBeNull();
        }

        [Theory, AutoDataWithDateOnly]
        public async Task AddLoan_ValidLoan_ReturnsLoan(LoanEntity entity, Loan model)
        {
            _mapperMock.Setup(x => x.Map<LoanEntity>(It.IsAny<Loan>())).Returns(entity);
            _loanRepoMock.Setup(x => x.Update(entity, default, default)).ReturnsAsync(() => entity);
            _mapperMock.Setup(x => x.Map<Loan>(It.IsAny<LoanEntity>())).Returns(model);

            var result = await _loanService.Add(model, default);

            result.ShouldBeEquivalentTo(model);
        }

        [Fact]
        public async Task AddLoan_InvalidLoan_ReturnsNull()
        {
            var entity = InvalidLoanEntities.NullLoanEntity;
            var model = InvalidLoanModels.NullLoan;

            _mapperMock.Setup(x => x.Map<LoanEntity>(It.IsAny<Loan>())).Returns(entity);
            _loanRepoMock.Setup(x => x.Update(It.IsAny<LoanEntity>(), default, default)).ReturnsAsync(entity);
            _mapperMock.Setup(x => x.Map<Loan>(It.IsAny<LoanEntity>())).Returns(model);

            var result = await _loanService.Add(model, default);

            result.ShouldBeNull();
        }

        [Theory]
        [InlineData(ProductType.LineOfCredit, LoanOrigin.Normal)]
        [InlineData(ProductType.InHouseCredit, LoanOrigin.Factoring)]
        [InlineData(ProductType.ARAdvance, LoanOrigin.Express)]
        public async Task CreateLoan_WithValidProductType_SetsLoanOriginCorrectly(ProductType productType, LoanOrigin expectedLoanOrigin)
        {
            var loanTemplateId = new Guid();
            var loan = SimpleValidModels.GetValidLoan();
            var loanTemplate = ValidLoanTemplateEntities.LoanTemplateEntityWithProduct(productType);
            var createLoan = SimpleValidModels.GetValidCreateLoanWithLoanTemplateId(loanTemplateId);

            _loanTemplateRepositoryMock.Setup(x => x.GetById(loanTemplateId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(loanTemplate);
            _createLoanServiceMock.Setup(x => x.Create(It.IsAny<CreateLoan>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(loan);

            var result = await _loanService.Create(createLoan, CancellationToken.None);

            result.ShouldBeEquivalentTo(loan);
            createLoan.LoanOrigin.ShouldBe(expectedLoanOrigin);
            _createLoanServiceMock.Verify(x => x.Create(It.IsAny<CreateLoan>(), It.IsAny<CancellationToken>()));
        }

        [Fact]
        public async Task CreateLoan_WhenLoanTemplateIsNull_DoesNotSetLoanOrigin()
        {
            var loanTemplateId = new Guid();
            var loan = SimpleValidModels.GetValidLoan();
            var createLoan = SimpleValidModels.GetValidCreateLoanWithLoanTemplateId(loanTemplateId);

            _loanTemplateRepositoryMock.Setup(x => x.GetById(loanTemplateId, It.IsAny<CancellationToken>()))
                .ReturnsAsync((LoanTemplateEntity)null);
            _createLoanServiceMock.Setup(x => x.Create(It.IsAny<CreateLoan>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(loan);

            var result = await _loanService.Create(createLoan, CancellationToken.None);

            result.ShouldBeEquivalentTo(loan);
            createLoan.LoanOrigin.ShouldBeNull();
            _createLoanServiceMock.Verify(x => x.Create(It.IsAny<CreateLoan>(), It.IsAny<CancellationToken>()));
        }

        [Theory, AutoDataWithDateOnly]
        public void GetPaidOnDate_ValidPredicate_ReturnsLoansList(IEnumerable<Loan> models,
            IEnumerable<LoanEntity> entities, DateTime date)
        {
            var fromDate = DateOnly.FromDateTime(date);
            var toDate = DateOnly.FromDateTime(date);

            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(It.IsAny<IEnumerable<LoanEntity>>())).Returns(models);
            _loanRepoMock.Setup(x => x.GetPaid(fromDate, toDate, default)).ReturnsAsync(entities);

            var result = _loanService.GetPaid(fromDate, toDate, false, default);

            result.ShouldNotBeNull();
            _loanRepoMock.Verify(x => x.GetPaid(fromDate, toDate, default), Times.Once);
        }

        [Theory, AutoDataWithDateOnly]
        public void GetUpcomingAndOverdueLoans_ValidPredicate_ReturnsLoansList(IEnumerable<Loan> models,
            IEnumerable<LoanEntity> entities, DateTime date)
        {
            var fromDate = DateOnly.FromDateTime(date);

            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(It.IsAny<IEnumerable<LoanEntity>>())).Returns(models);
            _loanRepoMock.Setup(x => x.GetUpcomingAndOverdueLoans(fromDate, default)).ReturnsAsync(entities);

            var result = _loanService.GetUpcomingAndOverdueLoans(fromDate, null, default);

            result.ShouldNotBeNull();
            _loanRepoMock.Verify(x => x.GetUpcomingAndOverdueLoans(fromDate, default), Times.Once);
        }

        [Fact]
        public Task UpdateLoanWithLoanReceivables_NullLoan_ReturnsVariableNullException()
        {
            LoanEntity loanEntity = null;

            _loanRepoMock.Setup(x => x.GetByIdWithTracking(It.IsAny<Guid>(), default)).ReturnsAsync(() => loanEntity);

            async Task MethodCallFunc() =>
                await _loanService.UpdateLoanReceivablesForLoan(It.IsAny<IEnumerable<LoanReceivable>>(),
                    It.IsAny<Guid>(), default, default);

            return Should.ThrowAsync<VariableNullException>(MethodCallFunc);
        }

        [Fact]
        public async Task UpdateLoanWithLoanReceivables_ValidData_ReturnsLoan()
        {
            var updatedInstallments = new List<LoanReceivable>()
            {
                new()
                {
                    PaidAmount = 1000,
                    ExpectedAmount = 2000,
                    Status = LoanReceivableStatus.Pending
                }
            };

            var loanInstallments = new List<LoanReceivableEntity>()
            {
                new()
                {
                    PaidAmount = 1000,
                    ExpectedAmount = 2000,
                    Status = LoanReceivableStatus.Pending
                }
            };

            var loanAmount = 2000;
            var loanFee = 0;

            var loanEntity = new LoanEntity()
            {
                Id = new Guid(),
                Amount = loanAmount,
                Fee = loanFee,
                LoanReceivables = loanInstallments,
            };

            var installmentsRescheduler = new List<LoanReceivableEntity>()
            {
                new()
                {
                    PaidAmount = 1000,
                    ExpectedAmount = 2000,
                    Status = LoanReceivableStatus.Pending,
                }
            };

            _loanRepoMock.Setup(x => x.GetByIdWithTracking(It.IsAny<Guid>(), default)).ReturnsAsync(loanEntity);

            _loanRepoMock.Setup(x => x.UpdateWithPayments(It.IsAny<LoanEntity>(), default, default)).ReturnsAsync(loanEntity);

            _loanReceivableServiceMock.Setup(x => x.UpdateLoanReceivablesForLoan(It.IsAny<List<LoanReceivableEntity>>(),
                    It.IsAny<List<LoanReceivableEntity>>(), It.IsAny<Guid>(), default))
                .Returns(installmentsRescheduler);

            await _loanServiceWithMapper.UpdateLoanReceivablesForLoan(updatedInstallments, It.IsAny<Guid>(), default,
                default);

            loanEntity.Fee.ShouldBe(loanFee);
            _changeLogMock.Verify(x => x.AddUpdatedLoanReceivablesLog(It.IsAny<List<LoanReceivable>>(),
                It.IsAny<List<LoanReceivable>>(), It.IsAny<string>(), default, default));
        }

        [Fact]
        public async Task GetLoansToSync_LoansIsExist_CallLoanRepoForSyncTimeAndReturnLoanList()
        {
            // Arrange
            var dateTimeUtsNow = DateTime.UtcNow;

            var loansEntity = new List<LoanEntity>()
            {
                new() { LastSyncDate = dateTimeUtsNow - TimeSpan.FromDays(1), UpdatedAt = dateTimeUtsNow },
                new() { LastSyncDate = dateTimeUtsNow - TimeSpan.FromDays(1), UpdatedAt = dateTimeUtsNow }
            };

            var loansEntityAfterUpdateSyncTime = new List<LoanEntity>()
            {
                new() { LastSyncDate = dateTimeUtsNow },
                new() { LastSyncDate = dateTimeUtsNow }
            };

            var loansAfterMapper = new List<Loan>()
            {
                new() { LastSyncDate = dateTimeUtsNow },
                new() { LastSyncDate = dateTimeUtsNow }
            };

            _loanRepoMock.Setup(x => x.GetLoansToSync(default)).ReturnsAsync(loansEntity);
            _loanRepoMock.Setup(x => x.UpdateRangeSyncTime(loansEntity, default))
                .ReturnsAsync(loansEntityAfterUpdateSyncTime);
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(It.IsAny<LoanEntity>())).Returns(loansAfterMapper);

            // Act
            var result = await _loanServiceWithMapper.GetLoansToSync(null, default);

            // Assert
            result.ShouldBeAssignableTo<IEnumerable<Loan>>();
            _loanRepoMock.Verify(x => x.GetLoansToSync(default), Times.Once);
            _loanRepoMock.Verify(x => x.UpdateRangeSyncTime(loansEntity, default), Times.Once);
        }

        [Fact]
        public async Task GetLoansToSync_LoansIsNotExist_CallLoanRepoForSyncTimeAndReturnLoanList()
        {
            // Arrange
            _loanRepoMock.Setup(x => x.GetLoansToSync(default)).ReturnsAsync(value: null);

            // Act
            var result = await _loanServiceWithMapper.GetLoansToSync(false, default);

            // Assert
            _loanRepoMock.Verify(x => x.GetLoansToSync(default), Times.Once);
            _loanRepoMock.Verify(x => x.UpdateRangeSyncTime(It.IsAny<IEnumerable<LoanEntity>>(), default), Times.Never);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetByEinHashAndStatus_ValidData_ReturnsLoans(LoansByEinHashAndStatusFilter filter,
            RepositoryLoansByEinHashAndStatusFilter repositoryFilter, IEnumerable<LoanEntity> loanEntities, IEnumerable<Loan> loans)
        {
            _mapperMock.Setup(x => x.Map<RepositoryLoansByEinHashAndStatusFilter>(filter)).Returns(repositoryFilter);
            _loanRepoMock.Setup(x => x.GetByEinHashAndStatus(repositoryFilter, default)).ReturnsAsync(loanEntities);
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(loanEntities)).Returns(loans);

            var result = await _loanService.GetByEinHashAndStatus(filter, false, default);

            result.ShouldBe(loans);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetByPayableId_ValidData_ReturnsLoan(LoanEntity loan, List<Loan> loans)
        {
            var payableId = "payable";
            _loanRepoMock.Setup(x => x.GetByPayableId(payableId, default)).ReturnsAsync(loan);
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(It.Is<List<LoanEntity>>(loans => loans.Contains(loan)))).Returns(loans);

            var result = await _loanService.GetByPayableId(payableId, false, default);

            result.ShouldBe(loans);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetByDrawApprovalId_ValidData_ReturnsLoan(LoanEntity loan, List<Loan> loans)
        {
            var drawApprovalId = "drawApprovalId";
            _loanRepoMock.Setup(x => x.GetByDrawApprovalId(drawApprovalId, default)).ReturnsAsync(loan);
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(It.Is<List<LoanEntity>>(loans => loans.Contains(loan)))).Returns(loans);

            var result = await _loanService.GetByDrawApprovalId(drawApprovalId, false, default);

            result.ShouldBe(loans);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetByProduct_ValidData_ReturnsLoan(List<LoanEntity> loanEntities, List<Loan> loans)
        {
            var product = ProductType.ARAdvance;

            _loanRepoMock.Setup(x => x.GetByProduct(product, default)).ReturnsAsync(loanEntities);
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(loanEntities)).Returns(loans);

            var result = await _loanService.GetByProduct(product, false, default);

            result.ShouldBe(loans);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetByDrawApprovalIds_ValidData_ReturnLoans(List<LoanEntity> loanEntities, List<Loan> loans)
        {
            var ids = loanEntities.Select(x => x.DrawApprovalId).ToArray();
            _loanRepoMock.Setup(x => x.GetByDrawApprovalIds(ids, default)).ReturnsAsync(loanEntities);
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(loanEntities)).Returns(loans);

            var result = await _loanService.GetByDrawApprovalIds(ids, false, default);

            result.ShouldBeEquivalentTo(loans);
            _loanRepoMock.Verify(x => x.GetByDrawApprovalIds(ids, default), Times.Once());
            _mapperMock.Verify(x => x.Map<IEnumerable<Loan>>(loanEntities), Times.Once);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetByDownPaymentStatuses_ValidData_ReturnLoans(List<LoanEntity> loanEntities, List<Loan> loans)
        {
            var statues = new[] { "expired", "due" };
            _loanRepoMock.Setup(x => x.GetByDownPaymentStatuses(statues, default)).ReturnsAsync(loanEntities);
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(loanEntities)).Returns(loans);

            var result = await _loanService.GetByDownPaymentStatuses(statues, false, default);

            result.ShouldBeEquivalentTo(loans);
            _loanRepoMock.Verify(x => x.GetByDownPaymentStatuses(statues, default), Times.Once());
            _mapperMock.Verify(x => x.Map<IEnumerable<Loan>>(loanEntities), Times.Once);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetByDownPaymentExpireDate_ValidData_ReturnLoans(List<LoanEntity> loanEntities, List<Loan> loans)
        {
            var currentDate = DateTime.UtcNow.ToDateOnly();
            var fromDate = currentDate.AddDays(-7);
            var toDate = currentDate;
            _loanRepoMock.Setup(x => x.GetByDownPaymentExpireDate(fromDate, toDate, default)).ReturnsAsync(loanEntities);
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(loanEntities)).Returns(loans);

            var result = await _loanService.GetByDownPaymentExpireDate(fromDate, toDate, false, default);

            result.ShouldBeEquivalentTo(loans);
            _loanRepoMock.Verify(x => x.GetByDownPaymentExpireDate(fromDate, toDate, default), Times.Once());
            _mapperMock.Verify(x => x.Map<IEnumerable<Loan>>(loanEntities), Times.Once);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task HardDeleteByCompanyId_ValidData_ShouldNotThrowEx(string companyId, DateTime date,
            List<LoanEntity> loanEntities)
        {
            _loanRepoMock.Setup(x => x.Get(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default))
                .ReturnsAsync(loanEntities);

            await _loanService.HardDeleteByCompanyId(companyId, date, default);

            _loanRepoMock.Verify(x => x.HardDeleteRange(loanEntities, default), Times.AtLeastOnce);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetByProjectId_ValidData_ReturnsLoans(string projectId, IEnumerable<LoanEntity> loanEntities, IEnumerable<Loan> loans)
        {
            _loanRepoMock.Setup(x => x.Get(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default)).ReturnsAsync(loanEntities);
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(loanEntities)).Returns(loans);
            var result = await _loanService.GetByProjectId(projectId, false, default);

            result.ShouldBe(loans);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GetByProjectId_ValidData_ReturnsNull(string projectId)
        {
            _loanRepoMock.Setup(x => x.Get(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default)).ReturnsAsync(new List<LoanEntity>());
            _mapperMock.Setup(x => x.Map<IEnumerable<Loan>>(null)).Returns(new List<Loan>());
            var result = await _loanService.GetByProjectId(projectId, false, default);

            result.ShouldBeEmpty();
        }

        [Theory, AutoDataWithDateOnly]
        public async Task ChangePaymentProcessTemplate_ValidData_ShouldNotThrowEx(LoanEntity loan,
            PaymentProcessTemplateEntity template)
        {
            _paymentProcessTemplateRepository.Setup(x => x.GetById(template.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(template);

            _loanRepoMock.Setup(x => x.GetById(loan.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(loan);

            _loanRepoMock
                .Setup(x => x.UpdatePaymentProcessTemplate(loan.Id, template.Id, It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            _paymentProcessTemplateHistoryRepository
                .Setup(x => x.Add(It.IsAny<PaymentProcessTemplateHistoryEntity>(), It.IsAny<CancellationToken>(), null))
                .ReturnsAsync((PaymentProcessTemplateHistoryEntity)null);

            await _loanService.ChangePaymentProcessTemplate(loan.Id, template.Id, CancellationToken.None);

            _loanRepoMock.Verify(
                x => x.UpdatePaymentProcessTemplate(loan.Id, template.Id, It.IsAny<CancellationToken>()), Times.Once);
            _paymentProcessTemplateHistoryRepository.Verify(
                x => x.Add(It.IsAny<PaymentProcessTemplateHistoryEntity>(), It.IsAny<CancellationToken>(), null), Times.Once);
        }

        [Fact]
        public async Task SetupLoanSettings_LoanNotFound_ThrowsException()
        {
            _loanRepoMock.Setup(x => x.GetByIdWithTracking(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((LoanEntity)null);

            var loanSettings = new LoanSettingsModel();
            var loanId = Guid.NewGuid();
            var userId = "testUser";

            await Assert.ThrowsAsync<VariableNullException>(() =>
                _loanService.SetupLoanSettings(loanId, userId, loanSettings, CancellationToken.None));
        }

        [Theory, AutoDataWithDateOnly]
        public async Task SetupLoanSettings_SkipLateFeeGeneration_ChangesLogged(LoanEntity loanEntity, LoanSettingsModel loanSettings)
        {
            loanEntity.SkipLateFeeGeneration = false;
            loanSettings.SkipLateFeeGeneration = true;
            loanSettings.SkipPenaltyGeneration = null;

            var userId = "testUser";

            _loanRepoMock.Setup(x => x.GetByIdWithTracking(loanEntity.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(loanEntity);

            await _loanService.SetupLoanSettings(loanEntity.Id, userId, loanSettings, CancellationToken.None);

            _changeLogMock.Verify(x => x.Add(It.IsAny<ChangeLog>(), CancellationToken.None), Times.Once);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task SetupLoanSettings_SkipLateFeeGenerationWithOldValue_ChangesNotLogged(LoanEntity loanEntity, LoanSettingsModel loanSettings)
        {
            loanEntity.SkipLateFeeGeneration = true;
            loanSettings.SkipLateFeeGeneration = true;
            loanSettings.SkipPenaltyGeneration = null;

            var userId = "testUser";

            _loanRepoMock.Setup(x => x.GetByIdWithTracking(loanEntity.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(loanEntity);

            await _loanService.SetupLoanSettings(loanEntity.Id, userId, loanSettings, CancellationToken.None);

            _changeLogMock.Verify(x => x.Add(It.IsAny<ChangeLog>(), CancellationToken.None), Times.Never);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task SetupLoanSettings_SkipPenaltyGeneration_ChangesLogged(LoanEntity loanEntity, LoanSettingsModel loanSettings)
        {
            loanEntity.SkipPenaltyGeneration = false;
            loanSettings.SkipPenaltyGeneration = true;
            loanSettings.SkipLateFeeGeneration = null;

            var userId = "testUser";

            _loanRepoMock.Setup(x => x.GetByIdWithTracking(loanEntity.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(loanEntity);

            await _loanService.SetupLoanSettings(loanEntity.Id, userId, loanSettings, CancellationToken.None);

            _changeLogMock.Verify(x => x.Add(It.IsAny<ChangeLog>(), CancellationToken.None), Times.Once);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task SetupLoanSettings_SkipPenaltyGenerationWithOldValue_ChangesNotLogged(LoanEntity loanEntity, LoanSettingsModel loanSettings)
        {
            loanEntity.SkipPenaltyGeneration = true;
            loanSettings.SkipPenaltyGeneration = true;
            loanSettings.SkipLateFeeGeneration = null;

            var userId = "testUser";

            _loanRepoMock.Setup(x => x.GetByIdWithTracking(loanEntity.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(loanEntity);

            await _loanService.SetupLoanSettings(loanEntity.Id, userId, loanSettings, CancellationToken.None);

            _changeLogMock.Verify(x => x.Add(It.IsAny<ChangeLog>(), CancellationToken.None), Times.Never);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task SetupLoanSettings_UpdatesLoanInRepository(LoanEntity loanEntity, LoanSettingsModel loanSettings)
        {
            var userId = "testUser";

            _loanRepoMock.Setup(x => x.GetByIdWithTracking(loanEntity.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(loanEntity);

            await _loanService.SetupLoanSettings(loanEntity.Id, userId, loanSettings, CancellationToken.None);

            _loanRepoMock.Verify(x => x.Update(loanEntity, CancellationToken.None, null), Times.Once);
        }

        [Fact]
        public async Task ChangeLoanAutoCollection_LoanNotFound_ThrowsVariableNullException()
        {
            // Arrange
            var loanId = Guid.NewGuid();
            var userId = "user";
            _loanRepoMock.Setup(r => r.GetById(loanId, It.IsAny<CancellationToken>()))
                .ReturnsAsync((LoanEntity?)null);

            // Act & Assert
            await Assert.ThrowsAsync<VariableNullException>(() =>
                _loanService.ChangeLoanAutoCollection(loanId, userId, true, CancellationToken.None));
        }

        [Fact]
        public async Task ChangeLoanAutoCollection_NoChange_DoesNotUpdate()
        {
            // Arrange
            var loanId = Guid.NewGuid();
            var userId = "user";
            var loan = new LoanEntity { Id = loanId, IsAutoCollectionPaused = true };
            _loanRepoMock.Setup(r => r.GetById(loanId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(loan);

            // Act
            await _loanService.ChangeLoanAutoCollection(loanId, userId, true, CancellationToken.None);

            // Assert
            _loanRepoMock.Verify(r => r.Update(It.IsAny<LoanEntity>(), It.IsAny<CancellationToken>(), null), Times.Never);
        }

        [Fact]
        public async Task ChangeLoanAutoCollection_ChangeValue_UpdatesLoan()
        {
            // Arrange
            var loanId = Guid.NewGuid();
            var userId = "user123";
            var loan = new LoanEntity { Id = loanId, IsAutoCollectionPaused = false };
            _loanRepoMock.Setup(r => r.GetById(loanId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(loan);
            _loanRepoMock.Setup(r => r.Update(loan, It.IsAny<CancellationToken>(), null))
                .ReturnsAsync(loan);

            // Act
            await _loanService.ChangeLoanAutoCollection(loanId, userId, true, CancellationToken.None);

            // Assert
            loan.IsAutoCollectionPaused.ShouldBeTrue();
            loan.AutoCollectionPausedBy.ShouldBe(userId);
            loan.AutoCollectionPausedAt.ShouldNotBeNull();
            _loanRepoMock.Verify(r => r.Update(loan, It.IsAny<CancellationToken>(), null), Times.Once);
        }
    }
}