# Generic variables
variable "location" {
  type    = string
  default = "West US"
}

variable "environment" {
  type    = string
  default = "qa"
}

variable "application_name" {
  type = map(any)
  default = {
    full = "loan-management-service",
    slug = "lms"
  }
}

variable "keyvault_unique_name" {
  type    = string
  default = "keyvault-qa-b2c7f314826"
}

variable "container_registry_name" {
  type    = string
  default = "containerRegistryBTQa"
}

variable "image_version" {
  type    = string
  default = "latest"
}

variable "acr_password" {
  description = "ACR password"
  default = ""
}

variable "client_id" {
  description = "ARM client id"
  default = ""
}

variable "tenant_id" {
  description = "ARM Tenant id"
  default = ""
}

variable "client_secret" {
  description = "ARM client secret"
  default = ""
}

variable "storage_account_access_key" {
  description = "Storage account access key"
  default = ""
}

variable "environment_camelcase_map" {
  type = map(any)
  default = {
    dev  = "Dev"
    qa   = "Qa"
    beta = "Beta"
    prod = "Prod"
  }
}

variable "functions_worker_runtime" {
    type    = string
    default = "dotnet-isolated"
}
variable "dotnet_version" {
    type    = string
    default = "8.0"
}

variable "function_extension_version" {
    type    = string
    default = "~4"
}

variable "aws_access_key_id" {
  description = "AWS access key id"
  default = ""
}

variable "aws_secret_access_key" {
  description = "AWS secret access key"
  default = ""
}

variable "aws_default_region" {
  description = "AWS default region"
  default = "us-west-1"
}

variable "lp_aws_account" {
  description = "AWS account id"
  default = ""
}

variable "subscription_id" {
  description = "Azure subscription ID"
  type        = string
}

variable "cpu" {
  type    = number
  description = "CPU size of container"
  default = 0.25
}

variable "memory" {
  type    = string
  description = "Memory size of container"
  default = "0.5Gi"
}

data "terraform_remote_state" "core" {
  backend = "azurerm"
  config = {
    resource_group_name  = var.environment
    storage_account_name = "tfstatebtqa03ca9fde916"
    container_name       = "tfstate-core-infra"
    key                  = "terraform.tfstate"
  }
}