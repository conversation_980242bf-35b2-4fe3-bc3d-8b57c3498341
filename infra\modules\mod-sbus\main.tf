# Service Bus Namespace
resource "azurerm_servicebus_namespace" "sbn" {
  name                = "service-bus-namespace-credit-status-${var.environment}"
  location            = var.resource_group_location
  resource_group_name = var.resource_group_name
  sku                 = "Standard"

  tags = {
    environment = title(var.environment)
    source      = "Terraform"
    app         = title(var.application_name.full)
    CreatedOn   = formatdate("YYYY-MM-DD hh:mm ZZZ", timestamp())
    Type        = "Microsoft Azure Container App"
  }

  lifecycle {
    ignore_changes = [
      tags["CreatedOn"]
    ]
  }

}

# Service Bus Queue
resource "azurerm_servicebus_queue" "credit_status" {
  name         = "creditstatus-${var.environment}"
  namespace_id = azurerm_servicebus_namespace.sbn.id
  lock_duration = "PT5M"
} 

resource "azurerm_key_vault_secret" "credit-status-queue-secret" {
  name         = "${var.credit_status_queue_connection}"
  value        = azurerm_servicebus_queue_authorization_rule.credit-status-queue-read-write-rule.primary_connection_string
  key_vault_id = var.key_vault_id
}

resource "azurerm_key_vault_secret" "credit-status-queue-name-secret" {
  name         = "${var.credit_status_queue_name}"
  value        = azurerm_servicebus_queue.credit_status.name
  key_vault_id = var.key_vault_id
}

resource "azurerm_servicebus_queue_authorization_rule" "credit-status-queue-read-write-rule" {
  name     = "credit-status-bus-queue-connection-auth-rule"
  queue_id = azurerm_servicebus_queue.credit_status.id
  listen = true
  send = true
  manage = true
}
