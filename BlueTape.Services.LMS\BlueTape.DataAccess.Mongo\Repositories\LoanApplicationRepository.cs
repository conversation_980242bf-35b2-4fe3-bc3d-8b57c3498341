﻿using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.DataAccess.Mongo.Documents;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.DataAccess.Mongo.Repositories;

public class LoanApplicationRepository(
    ILmsMongoDBContext context,
    ILogger<GenericRepository<LoanApplicationDocument>> logger)
    : GenericRepository<LoanApplicationDocument>(context, logger), ILoanApplicationRepository
{
    public async Task<List<LoanApplicationDocument>> GetApprovedLoanApplicationsByCompanyId(string companyId,
        CancellationToken ct)
    {
        var expression = Builders<LoanApplicationDocument>.Filter.Where(x => x.CompanyId == companyId &&
                                                                             (x.Status == "approved" ||
                                                                              x.Status == "closed"));

        return await Collection.Find(expression).ToListAsync(ct);
    }


    public async Task<List<LoanApplicationDocument>> GetRejectedLoanApplications(CancellationToken ct)
    {
        var expression = Builders<LoanApplicationDocument>.Filter.Where(x => x.Status == "rejected");

        return await Collection.Find(expression).ToListAsync(ct);
    }

    public async Task<List<LoanApplicationDocument>> GetApprovedLoanApplications(CancellationToken ct)
    {
        var expression = Builders<LoanApplicationDocument>.Filter.Where(x => x.Status == "approved");

        return await Collection.Find(expression).ToListAsync(ct);
    }

    public async Task<LoanApplicationDocument?> GetByLmsId(string lmsId, CancellationToken ct)
    {
        var expression = Builders<LoanApplicationDocument>.Filter.Where(x => x.LmsId == lmsId);

        return await Collection.Find(expression).FirstOrDefaultAsync(ct);
    }

    public async Task<IReadOnlyCollection<LoanApplicationDocument>> GetByLmsIds(IReadOnlyCollection<string> lmsId,
        CancellationToken ct)
    {
        var filterBuilder = Builders<LoanApplicationDocument>.Filter;

        // Create the filter
        var filter = filterBuilder.And(
            filterBuilder.Ne(doc => doc.LmsId, null), // LmsId is not null
            filterBuilder.In(doc => doc.LmsId, lmsId) // LmsId is in the list
        );

        return await Collection.Find(filter).ToListAsync(ct);
    }
}