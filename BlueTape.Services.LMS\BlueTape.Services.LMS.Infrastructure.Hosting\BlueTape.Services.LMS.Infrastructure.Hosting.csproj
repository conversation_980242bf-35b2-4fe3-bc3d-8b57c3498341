﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.2" />
        <PackageReference Include="Azure.Identity" Version="1.10.4" />
        <PackageReference Include="BlueTape.AzureKeyVault" Version="1.0.3" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="1.1.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.21.0" />
        <PackageReference Include="Serilog.Enrichers.GlobalLogContext" Version="3.0.0" />
        <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.2" />
        <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\BlueTape.Services.LMS.Application\BlueTape.Services.LMS.Application.csproj" />
      <ProjectReference Include="..\BlueTape.Services.LMS.Infrastructure\BlueTape.Services.LMS.Infrastructure.csproj" />
    </ItemGroup>

</Project>
