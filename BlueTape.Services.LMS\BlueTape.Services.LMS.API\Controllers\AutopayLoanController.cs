using AutoMapper;
using BlueTape.LS.DTOs.Loan;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.ViewModels;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers;

[ApiController]
[Route(ControllersConstants.AutopayLoan)]
[Authorize]
public class AutopayLoanController
{
    private readonly IMapper _mapper;
    private readonly IAutoPayLoanService _autoPayLoanService;

    public AutopayLoanController(IAutoPayLoanService autoPayLoanService, IMapper mapper)
    {
        _mapper = mapper;
        _autoPayLoanService = autoPayLoanService;
    }

    /// <summary>
    /// Get loan for autopay
    /// </summary>
    /// <param name="autopayLoanQuery"></param>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /AutopayLoans?UpcomingDate=2022-10-20&amp;CountDaysForUpcomming=3,
    ///     GET /AutopayLoans
    ///     
    /// </remarks>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<AutoPayLoanDto>> GetUpcoming([FromQuery] AutopayLoanQuery autopayLoanQuery, CancellationToken ct)
    {
        var date = (autopayLoanQuery.UpcomingDate.HasValue) ? autopayLoanQuery.UpcomingDate.Value : DateOnly.FromDateTime(DateTime.UtcNow);
        var countDays = autopayLoanQuery.CountDaysForUpcoming;
        var result = await _autoPayLoanService.GetUpcoming(date, countDays, ct);
        return _mapper.Map<IEnumerable<AutoPayLoanDto>>(result);
    }
}
