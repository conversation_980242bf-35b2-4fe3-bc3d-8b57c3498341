﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BlueTape.AWSS3" Version="1.1.6" />
    <PackageReference Include="BlueTape.Notification.Sender" Version="1.0.3" />
    <PackageReference Include="Quartz" Version="3.8.0" />
    <PackageReference Include="Quartz.Extensions.DependencyInjection" Version="3.8.0" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlueTape.DataAccess.Mongo\BlueTape.DataAccess.Mongo.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.DataAccess\BlueTape.Services.LMS.DataAccess.csproj" />
    <ProjectReference Include="..\BlueTape.Services.Reporting\BlueTape.Services.Reporting.csproj" />
  </ItemGroup>

</Project>
