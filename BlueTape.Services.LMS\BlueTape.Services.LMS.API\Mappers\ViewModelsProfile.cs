﻿using AutoMapper;
using AutoMapper.Extensions.EnumMapping;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.Domain.Models;
using BlueTape.LS.DTOs;
using BlueTape.LS.DTOs.AuthorizationPeriods;
using BlueTape.LS.DTOs.CardPricingPackages;
using BlueTape.LS.DTOs.ChangeLog;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Credit.CreditDetails;
using BlueTape.LS.DTOs.Enums;
using BlueTape.LS.DTOs.Loan;
using BlueTape.LS.DTOs.Loan.PayablesDetails;
using BlueTape.LS.DTOs.Loan.ReceivablesReschedule;
using BlueTape.LS.DTOs.LoanParameters;
using BlueTape.LS.DTOs.LoanPayables;
using BlueTape.LS.DTOs.LoanPricingPackages;
using BlueTape.LS.DTOs.LoanReceivable;
using BlueTape.LS.DTOs.LoanReceivable.LatePaymentFees;
using BlueTape.LS.DTOs.LoanReceivablesPayments;
using BlueTape.LS.DTOs.LoanTemplates;
using BlueTape.LS.DTOs.Payment;
using BlueTape.LS.DTOs.PenaltyInterest;
using BlueTape.LS.DTOs.PenaltyInterest.BasisPoint;
using BlueTape.LS.DTOs.Plan;
using BlueTape.Services.LMS.API.Query;
using BlueTape.Services.LMS.Application.Models;
using BlueTape.Services.LMS.Application.Models.AuthorizationPeriods;
using BlueTape.Services.LMS.Application.Models.CardPricingPackages;
using BlueTape.Services.LMS.Application.Models.ChangeLog;
using BlueTape.Services.LMS.Application.Models.Credits;
using BlueTape.Services.LMS.Application.Models.LoanParameters;
using BlueTape.Services.LMS.Application.Models.LoanPayables;
using BlueTape.Services.LMS.Application.Models.LoanPricingPackages;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.LMS.Application.Models.LoanReceivables.LatePaymentFees;
using BlueTape.Services.LMS.Application.Models.LoanReceivablesPayments;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Application.Models.Loans.PayablesDetails;
using BlueTape.Services.LMS.Application.Models.LoanTemplates;
using BlueTape.Services.LMS.Application.Models.Payments;
using BlueTape.Services.LMS.Application.Models.PenaltyInterest;
using BlueTape.Services.LMS.Application.Models.PenaltyInterest.BasisPoint;
using BlueTape.Services.LMS.Application.Models.Plan;
using BlueTape.Services.LMS.Application.Models.Timeline;
using BlueTape.Services.LMS.Domain.Entities.Filters;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Utilities.Models;
using TinyHelpers.Extensions;
using CreditStatus = BlueTape.OBS.Enums.CreditStatus;
using InputLatePaymentFeeStatus = BlueTape.LS.DTOs.Enums.InputLatePaymentFeeStatus;
using LoanDueStatus = BlueTape.Services.LMS.Domain.Enums.LoanDueStatus;
using LoanReceivableStatus = BlueTape.Services.LMS.Domain.Enums.LoanReceivableStatus;
using LoansByEinHashAndStatusFilter = BlueTape.Services.LMS.Application.Models.Filters.LoansByEinHashAndStatusFilter;
using LoanStatus = BlueTape.Services.LMS.Domain.Enums.LoanStatus;
using PaginatedLoanQuery = BlueTape.Services.LMS.Domain.Entities.PaginatedLoanQuery;
using PaymentStatus = BlueTape.Services.LMS.Domain.Enums.PaymentStatus;
using PaymentSubType = BlueTape.LS.Domain.Enums.PaymentSubType;
using PaymentType = BlueTape.LS.Domain.Enums.PaymentType;
using ProductType = BlueTape.Services.LMS.Domain.Enums.ProductType;
using FundingSource = BlueTape.Services.LMS.Domain.Enums.FundingSource;

namespace BlueTape.Services.LMS.API.Mappers
{
    public class ViewModelsProfile : Profile
    {
        public ViewModelsProfile()
        {

            CreateMap<LoanTemplateQuery, LoanTemplateFilter>();

            CreateMap<TimelineItem, LoanReceivablesPaymentsTimelineItemDto>();

            CreateMap<LoanStatus, InputLoanStatus>().ConvertUsingEnumMapping(opt => opt
                    .MapByName()
                )
                .ReverseMap();

            CreateMap<LatePaymentFeeStatus, InputLatePaymentFeeStatus>().ConvertUsingEnumMapping(opt => opt
                    .MapByName()
                )
                .ReverseMap();

            CreateMap<PaymentStatus, InputPaymentStatus>()
                .ConvertUsingEnumMapping(opt => opt.MapByName())
                .ReverseMap();
            CreateMap<PaymentType, InputPaymentType>()
                .ConvertUsingEnumMapping(opt => opt.MapByName())
                .ReverseMap();
            CreateMap<LoanReceivableStatus, InputReceivableFeeStatus>()
                .ConvertUsingEnumMapping(opt => opt.MapByName())
                .ReverseMap();
            CreateMap<LoanStatus, InputAdminLoanStatus>()
                .ConvertUsingEnumMapping(opt => opt.MapByName())
                .ReverseMap();

            CreateMap<CreditStatus, InputCreditStatus>().ConvertUsingEnumMapping(opt => opt
                    .MapByName()
                )
                .ReverseMap();

            CreateMap<LoanQuery, LoansByEinHashAndStatusFilter>()
                .ForMember(x => x.Status, opt => opt.MapFrom(y => y.LoanStatus));

            //--------------------------------------- Mapping from LS API Layer
            CreateMap<ChangeLog, ChangeLogDto>().ReverseMap();
            CreateMap<CreateChangeLogDto, ChangeLog>();

            MapLoans();
            MapCredits();
            MapLoanTemplates();
            MapLoanReceivables();
            MapPayments();
            MapLoanPricingPackages();
            MapCardPricingPackages();
            AddAuthPeriodMapping();
        }

        private void MapLoanTemplates()
        {
            CreateMap<LoanTemplate, LoanTemplateDto>().ReverseMap();
            CreateMap<LoanParameters, LoanParametersDto>().ReverseMap();
            CreateMap<LoanTemplate, ShortLoanTemplateDto>().ReverseMap();
            CreateMap<LoanTemplatesQuery, LoanTemplateFilter>();
        }

        private void MapCredits()
        {
            CreateMap<UpdateCreditDetailsDto, UpdateCreditDetailsModel>();
            CreateMap<UpdateCreditDetailsDto, UpdateCreditDetails>();
            CreateMap<CreditDetails, CreditDetailsDto>();
            CreateMap<CreditFilterDto, CreditFilterModel>();
            CreateMap<UpdateManualCreditStatusDto, UpdateManualCreditStatusModel>();
            CreateMap<CreditStatus, CreditStatus>().ConvertUsingEnumMapping(opt => opt
                    .MapByName()
                )
                .ReverseMap();
            CreateMap<CreateCreditDto, Credit>()
                .ForMember(x => x.Currency, opt => opt.MapFrom(y => GetCreditCurrency(y)));
            CreateMap<Credit, CreditDto>()
                .ForMember(s => s.Status, opt => opt.MapFrom(d => d.ManualStatus ?? d.Status))
                .ForMember(x => x.IsCreditStatusManuallySet, opt => opt.MapFrom(d => d.ManualStatus != null));

        }

        private void MapLoans()
        {
            CreateMap<Loan, LoanDto>().ReverseMap();
            CreateMap<RefinanceLoanDto, RefinanceLoanModel>()
                .ForMember(x => x.LoansToRefinance,
                y => y.MapFrom(dto => dto.LoansToRefinanceDto));
            CreateMap<LoanToRefinanceDto, LoanToRefinanceModel>();
            CreateMap<CreateLoanDto, CreateLoan>()
                .ForMember(dest => dest.DownPaymentAmount, opt => opt.MapFrom(src => src.DownPaymentAmount ?? 0m))
                .ForMember(dest => dest.DownPaymentPercentage, opt => opt.MapFrom(src => src.DownPaymentPercentage ?? 0m))
                .ReverseMap();
            CreateMap<AutoPayLoan, AutoPayLoanDto>().ReverseMap();
            CreateMap<LoanReplanDto, LoanReplan>().ReverseMap();
            CreateMap<PaginatedResponse<LoanDto>, PaginatedResponse<Loan>>().ReverseMap();
            CreateMap<LoanQueryDto, LoansByEinHashAndStatusFilter>()
                .ForMember(x => x.Status, opt => opt.MapFrom(y => y.LoanStatus));
            CreateMap<UpdateLoanStatusAdminDto, ChangeLoanStatusModel>().ReverseMap();
            CreateMap<CalculatorLoanDto, CalculatorLoan>().ReverseMap();
            CreateMap<LoanPayablesDetails, LoanPayablesDetailsDto>();
            CreateMap<LoanDetails, LoanDetailsDto>();
            CreateMap<UpdateLoanDto, ChangeLoanStatusModel>();
            CreateMap<ReceivableTypeAmount, ReceivableTypeAmountDto>();
            CreateMap<LoanPayablesDto, LoanPayables>().ReverseMap();
            CreateMap<LoanPayablesCreateDto, LoanPayablesCreateModel>().ReverseMap();
            CreateMap<InputAdminLoanStatus, LoanStatus>().ConvertUsingEnumMapping(opt => opt.MapByName()).ReverseMap();
            CreateMap<ChangeLoanStatus, InputLoanStatus>().ConvertUsingEnumMapping(opt => opt.MapByName()).ReverseMap();
            CreateMap<LoanDueStatus, LS.Domain.Enums.LoanDueStatus>().ConvertUsingEnumMapping(opt => opt.MapByName()).ReverseMap();
            CreateMap<LS.Domain.Enums.ProductType, ProductType>().ConvertUsingEnumMapping(opt => opt.MapByName()).ReverseMap();
            CreateMap<LS.Domain.Enums.FundingSource, FundingSource>().ConvertUsingEnumMapping(opt => opt.MapByName()).ReverseMap();
            CreateMap<LS.Domain.Models.PaginatedLoanQuery, PaginatedLoanQuery>().ReverseMap();
            CreateMap<LoanSettingsDto, LoanSettingsModel>().ReverseMap();
            CreateMap<LS.Domain.Enums.DownPaymentStatus, Domain.Enums.DownPaymentStatus>().ReverseMap();
        }

        private void AddAuthPeriodMapping()
        {
            CreateMap<AuthorizationPeriodModel, AuthorizationPeriodDto>().ReverseMap();
            CreateMap<AuthorizationPeriodQueryDto, AuthorizationPeriodQueryModel>();
            CreateMap<CreditHoldQueryDto, CreditHoldQueryModel>();
            CreateMap<CreateAuthorizationPeriodDto, CreateAuthorizationPeriodModel>();
            CreateMap<PatchCreditHoldExpirationDateDto, PatchCreditHoldExpirationDateModel>();
        }

        private void MapLoanReceivables()
        {
            CreateMap<LoanReceivable, LoanReceivableDto>().ReverseMap();
            CreateMap<LoanReceivable, UpdateLoanReceivableDto>().ReverseMap();
            CreateMap<CalculatorLoanReceivable, CalculatorLoanReceivableDto>().ReverseMap();
            CreateMap<UpdateLoanReceivableDateDto, UpdateLoanReceivableDateModel>().ReverseMap();
            CreateMap<LoanReceivable, UpdateLoanReceivableDto>().ReverseMap();
            CreateMap<LoanReceivable, CreateLoanReceivableDto>().ReverseMap();
            CreateMap<LoanReceivableStatus, InputReceivableFeeStatus>().ConvertUsingEnumMapping(opt => opt.MapByName()).ReverseMap();
            CreateMap<ResultWithPaginationDto<LoanReceivableDto>, ResultWithPagination<LoanReceivable>>().ReverseMap();
            CreateMap<LoanReceivablesPaymentsDto, LoanReceivablesPayments>().ReverseMap();
            CreateMap<UpdatePenaltyInterestFeeDto, UpdatePenaltyInterestFeeReceivable>();
            CreateMap<UpdateReceivableFeeDto, UpdateReceivableFeeModel>().ReverseMap();
            CreateMap<CreateFeeReceivableDto, CreateFeeReceivable>().ReverseMap();
            CreateMap<PenaltyInterestDetails, PenaltyInterestDetailsDto>();
            CreateMap<CreatePenaltyInterestFeeReceivableDto, CreatePenaltyInterestFeeReceivable>();
            CreateMap<BasisPoint, BasisPointDto>();
            CreateMap<ShortBasisPointDto, BasisPoint>();
            CreateMap<ReceivablesRescheduleDto, ReceivablesReschedule>();
            CreateMap<ReceivablesRescheduleItemDto, ReceivablesRescheduleItem>();
            CreateMap<TimelineItem, LoanReceivablesPaymentsTimelineItemDto>();
            CreateMap<LoanReceivablePlan, LoanReceivablePlanDto>().ReverseMap();
            CreateMap<ChangeReceivablesPaymentsResultItem, ChangeReceivablesPaymentsItemDto>();
        }

        private void MapPayments()
        {
            CreateMap<Payment, PaymentDto>().ReverseMap();
            CreateMap<Payment, ShortPaymentDto>().ReverseMap();
            CreateMap<CreatePaymentModel, CreatePaymentDto>().ReverseMap();
            CreateMap<NextPaymentDetailsDto, NextPaymentDetails>().ReverseMap();
            CreateMap<PaymentStatus, InputPaymentStatus>().ConvertUsingEnumMapping(opt => opt.MapByName()).ReverseMap();
            CreateMap<PaymentType, InputPaymentType>().ConvertUsingEnumMapping(opt => opt.MapByName()).ReverseMap();
            CreateMap<ChangePaymentResult, ChangePaymentResultDto>();
            CreateMap<CreatePaymentDto, CreatePaymentModel>()
                .ForMember(x => x.Id, opt => opt.MapFrom(y => y.LoanId))
                .ForMember(x => x.Pay, opt => opt.MapFrom(y => y.Amount))
                .ForMember(x => x.SubType, opt => opt.MapFrom(y => PaymentSubType.Repayment));
            CreateMap<ManualPaymentDto, ManualPayment>()
                .ForMember(x => x.SubType, opt => opt.MapFrom(y => PaymentSubType.Repayment));
            CreateMap<CreateRefundPaymentDto, CreatePaymentModel>()
                .ForMember(x => x.Type, opt => opt.MapFrom(y => LS.Domain.Enums.PaymentType.Custom))
                .ForMember(x => x.SubType, opt => opt.MapFrom(y => PaymentSubType.Refund))
                .ForMember(x => x.Id, opt => opt.MapFrom(y => y.LoanId))
                .ForMember(x => x.Pay, opt => opt.MapFrom(y => Math.Abs(y.Amount) * -1))
                .ForMember(x => x.PaymentDate, opt => opt.MapFrom(y => y.Date));
        }

        private void MapCardPricingPackages()
        {
            CreateMap<CardPricingPackageDto, CardPricingPackage>().ReverseMap();
            CreateMap<ShortCardPricingPackageDto, ShortCardPricingPackage>();
            CreateMap<Metadata, MetadataDto>().ReverseMap();
            CreateMap<Merchant, MerchantDto>().ReverseMap();
            CreateMap<Customer, CustomerDto>().ReverseMap();
            CreateMap<Ach, AchDto>().ReverseMap();
            CreateMap<Amex, AmexDto>().ReverseMap();
            CreateMap<CreditCardBusiness, CreditCardBusinessDto>().ReverseMap();
            CreateMap<CreditCardDiscover, CreditCardDiscoverDto>().ReverseMap();
            CreateMap<CreditCardDiscover2, CreditCardDiscover2Dto>().ReverseMap();
            CreateMap<CreditCardMasterCard, CreditCardMasterCardDto>().ReverseMap();
            CreateMap<CreditCardMasterCard2, CreditCardMasterCard2Dto>().ReverseMap();
            CreateMap<CreditCardTravel, CreditCardTravelDto>().ReverseMap();
            CreateMap<CreditCardVisa, CreditCardVisaDto>().ReverseMap();
            CreateMap<CreditCardVisa2, CreditCardVisa2Dto>().ReverseMap();
            CreateMap<DebitCardRegulated, DebitCardRegulatedDto>().ReverseMap();
            CreateMap<DebitCardUnregulated, DebitCardUnregulatedDto>().ReverseMap();
        }

        private void MapLoanPricingPackages()
        {
            CreateMap<LoanPricingPackageDto, LoanPricingPackage>().ReverseMap();
            CreateMap<ShortLoanPricingPackageDto, ShortLoanPricingPackage>().ReverseMap();
            CreateMap<LoanPricingPackageMetadataDto, LoanPricingPackageMetadata>().ReverseMap();
            CreateMap<LoanPricingPackagesFilter, BlueTape.DataAccess.Mongo.Documents.Filters.LoanPricingPackagesFilter>().ReverseMap();
        }

        private static string GetCreditCurrency(CreateCreditDto credit) =>
            (credit.Currency?.IsEmpty() ?? true) ? "USD" : credit.Currency;
    }
}