﻿using BlueTape.Services.LMS.Application.Models.Filters;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Utilities.Models;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices
{
    public interface ILoanService : IGenericService<Loan>
    {
        Task ChangeLoanStatus(ChangeLoanStatusModel changeLoanStatusModel, CancellationToken cancellationToken);
        Task<Loan> Create(CreateLoan createLoan, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetLoansToSync(bool? detailed, CancellationToken ct);
        Task<Loan> GetById(Guid id, bool? detailed, CancellationToken ct);
        Task<IEnumerable<Loan>> Get(bool? detailed, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetPaid(DateOnly fromDate, DateOnly toDate, bool? detailed, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetByProjectId(string projectId, bool? detailed, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetByDrawApprovalId(string drawApprovalId, bool? detailed, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetByDrawApprovalIds(string[] drawApprovalIds, bool? detailed, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetByDownPaymentStatuses(string[] downPaymentStatuses, bool? detailed, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetByDownPaymentExpireDate(DateOnly fromDate, DateOnly toDate, bool? detailed, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetOverdue(DateOnly fromDate, DateOnly toDate, bool? detailed, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetUpcomingAndOverdueLoans(DateOnly date, bool? detailed, CancellationToken ct);
        Task<Loan> UpdateLoanReceivablesForLoan(IEnumerable<LoanReceivable> loanReceivables, Guid loanId, string userId, CancellationToken ct, string? note = default);
        Task<IEnumerable<Loan>?> GetByIds(List<Guid> ids, bool? detailed, CancellationToken ct);
        Task<Loan> Refinance(RefinanceLoanModel refinanceLoans, string userId, CancellationToken ct);
        Task<Loan> ReplaceLoanReceivables(IEnumerable<LoanReceivable> loanReceivables, Guid loanId, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetByEinHashAndStatus(LoansByEinHashAndStatusFilter filter, bool? detailed, CancellationToken ct);
        Task HardDeleteByCompanyId(string companyId, DateTime date, CancellationToken cancellationToken);
        Task<PaginatedResponse<Loan>> GetByQuery(PaginatedLoanQuery queryParams, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetByProduct(ProductType product, bool? detailed, CancellationToken ct);
        Task<IEnumerable<Loan>?> GetByPayableId(string payableId, bool? detailed, CancellationToken ct);
        Task ChangePaymentProcessTemplate(Guid loanId, Guid paymentTemplateId, CancellationToken ct);
        Task ChangeLoanAutoCollection(Guid id, string userId, bool isAutoCollectionPaused, CancellationToken ct);
        Task<Loan> SetupLoanSettings(Guid loanId, string userId, LoanSettingsModel loanSettingsModel, CancellationToken ct);
    }
}
