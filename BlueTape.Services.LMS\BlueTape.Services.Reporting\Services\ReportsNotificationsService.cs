﻿using BlueTape.EmailSender.Abstractions;
using BlueTape.EmailSender.Models;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Constants;
using BlueTape.Services.Reporting.Models.LoanTapeReports;
using BlueTape.Services.Reporting.Models.LoanTapeReports.Notifications;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SendGrid.Helpers.Mail;
using System.Globalization;
using TinyHelpers.Extensions;

namespace BlueTape.Services.Reporting.Services;

public class ReportsNotificationsService(
    ISendGridBtClient sendGridClient,
    IOptions<EmailTemplateOptions> emailTemplateOptions,
    IConfiguration configuration,
    ILogger<ReportsNotificationsService> logger)
    : IReportsNotificationsService
{
    private readonly EmailTemplateOptions _emailTemplateOptions = emailTemplateOptions.Value;

    public async Task SendLoanTapeReportGeneratedNotification(GeneratedLoanTapeReportModel loanTapeReport, FundingSource fundingSource,
        CancellationToken ctx)
    {
        logger.LogInformation("Started sending email notification: Loan Tape report generated");
        var receiverEmails = GetReceiversEmails(fundingSource);
        if (receiverEmails.Length == 0)
        {
            logger.LogWarning("Loan Tape report email notification:" +
                              " notifications won't be sent as receiver email addresses not configured for {FundingSource}.", fundingSource);

            return;
        }

        logger.LogInformation(
            "Loan Tape report email notification: got necessary data. Started sending email messages.");

        var sendEmailAction = CreateSendEmailAction(loanTapeReport, ctx);
        await receiverEmails.ForEachAsync(sendEmailAction, ctx);

        logger.LogInformation("Loan Tape report email notification: sent all email messages.");
    }

    private Func<string, Task> CreateSendEmailAction(
        GeneratedLoanTapeReportModel loanTapeReport, CancellationToken ctx)
    {
        return async receiverEmail =>
        {
            try
            {
                var messageData = BuildEmailMessage(loanTapeReport);
                messageData.ToEmail = receiverEmail;
                await sendGridClient.SendTemplatedEmailAsync(messageData, ctx);
            }
            catch (Exception ex)
            {
                logger.LogError(ex,
                    "Loan Tape report email notification: failed to send email message to {ReceiverEmail}",
                    receiverEmail);
            }
        };
    }

    private EmailGridBtMessageModel<LoanTapeReportEmailMessageData> BuildEmailMessage(
        GeneratedLoanTapeReportModel loanTapeReport)
    {
        var templateId = _emailTemplateOptions.LoanTapeReportEmailTemplateId;
        var reportDate = loanTapeReport.ReportDate.ToString("d", CultureInfo.InvariantCulture);

        var messageData = new EmailGridBtMessageModel<LoanTapeReportEmailMessageData>()
        {
            TemplateId = templateId,
            TemplateData = new LoanTapeReportEmailMessageData
            {
                Date = reportDate,
                TotalAmount = loanTapeReport.TotalAmount.ToString(CultureInfo.InvariantCulture)
            },
            Attachments = new List<Attachment>
            {
                new()
                {
                    Content = loanTapeReport.Content,
                    Filename = loanTapeReport.FileName,
                    Disposition = ReportConstants.EmailAttachmentDisposition,
                    Type = ReportConstants.AttachmentContentTypes[loanTapeReport.FileFormat]
                }
            }
        };

        return messageData;
    }

    private string[] GetReceiversEmails(FundingSource fundingSource)
    {
        var emailAddressesKey = fundingSource switch
        {
            FundingSource.Arcadia => _emailTemplateOptions.ArcadiaLoanTapeReportEmailKey,
            FundingSource.Raistone => _emailTemplateOptions.RaistoneLoanTapeReportEmailKey,
            _ => null
        };

        if (emailAddressesKey is null)
        {
            return [];
        }

        var notificationReceiversEmails = configuration[emailAddressesKey ?? throw new VariableNullException(
            nameof(EmailTemplateOptions))];

        if (string.IsNullOrEmpty(notificationReceiversEmails)) return [];

        return notificationReceiversEmails.Split(',')
            .Where(s => !string.IsNullOrWhiteSpace(s))
            .Select(s => s.Trim())
            .Distinct()
            .ToArray();
    }
}