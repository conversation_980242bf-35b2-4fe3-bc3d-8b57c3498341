using BlueTape.Common.Extensions.Abstractions;
using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.Notification.Sender.Abstractions;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.OBS.Client.Abstractions;
using BlueTape.OBS.DTOs.DrawApproval.Responses;
using BlueTape.Services.LMS.Application.Services.LoanServices;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.SNS.SlackNotification.Models;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace BlueTape.Services.LMS.Application.Tests.Services
{
    public class LoanDownPaymentsServiceTests
    {
        private readonly LoanDownPaymentsService _loanDownPaymentsService;
        private readonly Mock<ILoanRepository> _loanRepositoryMock = new();
        private readonly Mock<IAzureNotificationSenderService> _azureNotificationSenderServiceMock = new();
        private readonly Mock<IUserRoleRepository> _userRoleRepositoryMock = new();
        private readonly Mock<IUserRepository> _userRepositoryMock = new();
        private readonly Mock<ILogger<LoanDownPaymentsService>> _loggerMock = new();
        private readonly Mock<ISlackNotificationService> _slackNotificationServiceMock = new();
        private readonly Mock<ITraceIdAccessor> _traceIdAccessorMock = new();
        private readonly Mock<IOnBoardingIntegrationExternalService> _onBoardingService = new();

        public LoanDownPaymentsServiceTests()
        {
            _traceIdAccessorMock.Setup(x => x.TraceId).Returns("test-trace-id");
            _loanDownPaymentsService = new LoanDownPaymentsService(
                _loanRepositoryMock.Object,
                _azureNotificationSenderServiceMock.Object,
                _userRoleRepositoryMock.Object,
                _userRepositoryMock.Object,
                _loggerMock.Object,
                _slackNotificationServiceMock.Object,
                _onBoardingService.Object,
                _traceIdAccessorMock.Object);
        }

        [Fact]
        public async Task CheckDownPaymentExpiration_NoLoansWithDueDownPayments_ShouldLogAndReturn()
        {
            // Arrange
            _loanRepositoryMock.Setup(x => x.GetByDownPaymentStatuses(
                    It.IsAny<string[]>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync((IEnumerable<LoanEntity>)null);

            // Act
            await _loanDownPaymentsService.CheckDownPaymentExpiration(CancellationToken.None);

            // Assert
            _loanRepositoryMock.Verify(x => x.GetByDownPaymentStatuses(
                It.Is<string[]>(statuses => statuses.Contains(DownPaymentStatus.Due.ToString())),
                It.IsAny<CancellationToken>()), Times.Once);

            _loanRepositoryMock.Verify(x => x.UpdateRangeWithPayments(
                It.IsAny<List<LoanEntity>>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task CheckDownPaymentExpiration_EmptyListOfLoans_ShouldLogAndReturn()
        {
            // Arrange
            _loanRepositoryMock.Setup(x => x.GetByDownPaymentStatuses(
                    It.IsAny<string[]>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<LoanEntity>());

            // Act
            await _loanDownPaymentsService.CheckDownPaymentExpiration(CancellationToken.None);

            // Assert
            _loanRepositoryMock.Verify(x => x.GetByDownPaymentStatuses(
                It.Is<string[]>(statuses => statuses.Contains(DownPaymentStatus.Due.ToString())),
                It.IsAny<CancellationToken>()), Times.Once);

            _loanRepositoryMock.Verify(x => x.UpdateRangeWithPayments(
                It.IsAny<List<LoanEntity>>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task CheckDownPaymentExpiration_ExpiredDownPayment_ShouldUpdateToExpiredStatus()
        {
            // Arrange
            var currentDate = DateTime.UtcNow;
            var loanId = Guid.NewGuid();
            var companyId = "test-company-id";

            var loan = new LoanEntity
            {
                Id = loanId,
                CompanyId = companyId,
                DownPaymentStatus = DownPaymentStatus.Due,
                DownPaymentStatusAt = currentDate.AddDays(-5),
                CreatedAt = currentDate.AddDays(-10),
                LoanParameters = new List<LoanParametersEntity>
                {
                    new LoanParametersEntity
                    {
                        IsActive = true,
                        DownPaymentExpireAt = currentDate.AddDays(-1) // Expired
                    }
                }
            };

            var loans = new List<LoanEntity> { loan };

            _loanRepositoryMock.Setup(x => x.GetByDownPaymentStatuses(
                    It.IsAny<string[]>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(loans);

            // Act
            await _loanDownPaymentsService.CheckDownPaymentExpiration(CancellationToken.None);

            // Assert
            _loanRepositoryMock.Verify(x => x.UpdateRangeWithPayments(
                It.Is<List<LoanEntity>>(loansToUpdate =>
                    loansToUpdate.Count == 1 &&
                    loansToUpdate[0].DownPaymentStatus == DownPaymentStatus.Expired),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CheckDownPaymentExpiration_NonExpiredDownPayment_ShouldNotUpdateStatus()
        {
            // Arrange
            var currentDate = DateTime.UtcNow;
            var loanId = Guid.NewGuid();
            var companyId = "test-company-id";

            var loan = new LoanEntity
            {
                Id = loanId,
                CompanyId = companyId,
                DownPaymentStatus = DownPaymentStatus.Due,
                DownPaymentStatusAt = currentDate.AddDays(-5),
                CreatedAt = currentDate.AddDays(-1), // Recently created
                LoanParameters = new List<LoanParametersEntity>
                {
                    new LoanParametersEntity
                    {
                        IsActive = true,
                        DownPaymentExpireAt = currentDate.AddDays(5) // Not expired
                    }
                }
            };

            var loans = new List<LoanEntity> { loan };

            _loanRepositoryMock.Setup(x => x.GetByDownPaymentStatuses(
                    It.IsAny<string[]>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(loans);

            // Act
            await _loanDownPaymentsService.CheckDownPaymentExpiration(CancellationToken.None);

            // Assert
            _loanRepositoryMock.Verify(x => x.UpdateRangeWithPayments(
                It.IsAny<List<LoanEntity>>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task CheckDownPaymentExpiration_NotificationNeeded_ShouldSendNotification()
        {
            // Arrange
            var currentDate = DateTime.UtcNow;
            var loanId = Guid.NewGuid();
            var drawApprovalId = Guid.NewGuid().ToString();
            var companyId = "test-company-id";

            var loan = new LoanEntity
            {
                Id = loanId,
                CompanyId = companyId,
                Status = LoanStatus.Created,
                DrawApprovalId = drawApprovalId,
                DownPaymentStatus = DownPaymentStatus.Due,
                DownPaymentStatusAt = currentDate.AddDays(-2), // Last status update was 2 days ago
                CreatedAt = currentDate.AddDays(-2), // Created 3 days ago (> 2 days threshold)
                LoanParameters = new List<LoanParametersEntity>
                {
                    new LoanParametersEntity
                    {
                        IsActive = true,
                        DownPaymentExpireAt = currentDate.AddDays(3) // Not expired yet
                    }
                }
            };

            var loans = new List<LoanEntity> { loan };
            var userRoles = new[]
            {
                new UserRoleDocument()
                {
                    CompanyId = companyId,
                    Status = "Active",
                    Sub = "user-sub-1",
                    Settings = new UserRoleSettingsDocument
                    {
                        PaymentEmailNotificationsEnabled = true
                    }
                }
            };

            var users = new List<UserDto>
            {
                new UserDto
                {
                    Sub = "user-sub-1",
                    Email = "<EMAIL>"
                }
            };

            var drawApprovals = new List<DrawApprovalDto> { new DrawApprovalDto() { Id = drawApprovalId, DownPaymentDetails = new DownPaymentDetailsDto() { PaymentMethod = "wire" } } };

            _loanRepositoryMock.Setup(x => x.GetByDownPaymentStatuses(
                    It.IsAny<string[]>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(loans);

            _userRoleRepositoryMock.Setup(x => x.GetByPredicate(
                    It.IsAny<Expression<Func<UserRoleDocument, bool>>>(),
                    It.IsAny<CancellationToken>()))
                .Returns(userRoles);

            _userRepositoryMock.Setup(x => x.GetBySub(
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(users);

            _onBoardingService.Setup(x => x.GetDrawApprovalsByInvoiceIds(
                    It.IsAny<string[]>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(drawApprovals);

            // Key change here - capture the notification being sent
            SystemNotificationDto capturedNotification = null;
            _azureNotificationSenderServiceMock
            .Setup(x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()))
            .Callback<SystemNotificationDto, CancellationToken>((n, ct) => capturedNotification = n)
                .Returns(Task.CompletedTask);

            // Act
            await _loanDownPaymentsService.CheckDownPaymentExpiration(CancellationToken.None);

            // Assert
            _azureNotificationSenderServiceMock.Verify(
x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()),
                Times.Once);

            // Verify notification properties
            Assert.NotNull(capturedNotification);
            Assert.Equal("DownPaymentReminder", capturedNotification.NotificationName);
            Assert.Contains(loanId.ToString(), capturedNotification.ReferenceIds);
            Assert.Equal(companyId, capturedNotification.CompanyId);
        }

        [Fact]
        public async Task CheckDownPaymentExpiration_ExceptionThrown_ShouldSendSlackNotification()
        {
            // Arrange
            _loanRepositoryMock.Setup(x => x.GetByDownPaymentStatuses(
                    It.IsAny<string[]>(),
                    It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Test exception"));

            _slackNotificationServiceMock.Setup(x => x.Notify(
                    It.IsAny<EventMessageBody>(),
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(async () =>
                await _loanDownPaymentsService.CheckDownPaymentExpiration(CancellationToken.None));

            _slackNotificationServiceMock.Verify(x => x.Notify(
                It.Is<EventMessageBody>(message =>
                    message.EventLevel == EventLevel.Error &&
                    message.EventName == "DownPaymentExpirationCheck"),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CheckDownPaymentExpiration_BothExpirationAndNotification_ShouldHandleBoth()
        {
            // Arrange
            var currentDate = DateTime.UtcNow;
            var loanId = Guid.NewGuid();
            var drawApprovalId = Guid.NewGuid().ToString();
            var companyId = "test-company-id";

            var loan = new LoanEntity
            {
                Id = loanId,
                CompanyId = companyId,
                Status = LoanStatus.Created,
                DrawApprovalId = drawApprovalId,
                DownPaymentStatus = DownPaymentStatus.Due,
                DownPaymentStatusAt = currentDate.AddDays(-2), // Last status update was 2 days ago
                CreatedAt = currentDate.AddDays(-2), // Created 3 days ago (> 2 days threshold)
                LoanParameters = new List<LoanParametersEntity>
                {
                    new LoanParametersEntity
                    {
                        IsActive = true,
                        DownPaymentExpireAt = currentDate.AddDays(-1) // Expired
                    }
                }
            };

            var loans = new List<LoanEntity> { loan };
            var userRoles = new[]
            {
                new UserRoleDocument
                {
                    CompanyId = companyId,
                    Status = "Active",
                    Sub = "user-sub-1",
                    Settings = new UserRoleSettingsDocument
                    {
                        PaymentEmailNotificationsEnabled = true
                    }
                }
            };

            var users = new List<UserDto>
            {
                new UserDto
                {
                    Sub = "user-sub-1",
                    Email = "<EMAIL>"
                }
            };

            var drawApprovals = new List<DrawApprovalDto> { new DrawApprovalDto() { Id = drawApprovalId, DownPaymentDetails = new DownPaymentDetailsDto() { PaymentMethod = "wire" } } };

            _loanRepositoryMock.Setup(x => x.GetByDownPaymentStatuses(
                    It.IsAny<string[]>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(loans);

            _userRoleRepositoryMock.Setup(x => x.GetByPredicate(
                    It.IsAny<Expression<Func<UserRoleDocument, bool>>>(),
                    It.IsAny<CancellationToken>()))
                .Returns(userRoles);

            _userRepositoryMock.Setup(x => x.GetBySub(
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(users);

            _onBoardingService.Setup(x => x.GetDrawApprovalsByInvoiceIds(
                    It.IsAny<string[]>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(drawApprovals);

            // Key change here - capture the notification being sent
            SystemNotificationDto capturedNotification = null;
            _azureNotificationSenderServiceMock
            .Setup(x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()))
            .Callback<SystemNotificationDto, CancellationToken>((n, ct) => capturedNotification = n)
                .Returns(Task.CompletedTask);

            // Act
            await _loanDownPaymentsService.CheckDownPaymentExpiration(CancellationToken.None);

            // Assert
            _loanRepositoryMock.Verify(x => x.UpdateRangeWithPayments(
                It.Is<List<LoanEntity>>(loansToUpdate =>
                    loansToUpdate.Count == 1 &&
                    loansToUpdate[0].DownPaymentStatus == DownPaymentStatus.Expired),
                It.IsAny<CancellationToken>()), Times.Once);

            // Verify the notification was sent
            _azureNotificationSenderServiceMock.Verify(
x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()),
                Times.Once);

            // Verify notification properties
            Assert.NotNull(capturedNotification);
            Assert.Equal("DownPaymentReminder", capturedNotification.NotificationName);
            Assert.Contains(loanId.ToString(), capturedNotification.ReferenceIds);
            Assert.Equal(companyId, capturedNotification.CompanyId);
        }

        [Fact]
        public async Task CheckDownPaymentExpiration_MissingCompanyId_ShouldSkipNotification()
        {
            // Arrange
            var currentDate = DateTime.UtcNow;
            var loanId = Guid.NewGuid();

            var loan = new LoanEntity
            {
                Id = loanId,
                CompanyId = null, // Missing company ID
                DownPaymentStatus = DownPaymentStatus.Due,
                DownPaymentStatusAt = currentDate.AddDays(-2), // Last status update was 2 days ago
                CreatedAt = currentDate.AddDays(-3), // Created 3 days ago (> 2 days threshold)
                LoanParameters = new List<LoanParametersEntity>
                {
                    new LoanParametersEntity
                    {
                        IsActive = true,
                        DownPaymentExpireAt = currentDate.AddDays(-1) // Expired
                    }
                }
            };

            var loans = new List<LoanEntity> { loan };

            _loanRepositoryMock.Setup(x => x.GetByDownPaymentStatuses(
                    It.IsAny<string[]>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(loans);

            // Act
            await _loanDownPaymentsService.CheckDownPaymentExpiration(CancellationToken.None);

            // Assert
            _loanRepositoryMock.Verify(x => x.UpdateRangeWithPayments(
                It.Is<List<LoanEntity>>(loansToUpdate =>
                    loansToUpdate.Count == 1 &&
                    loansToUpdate[0].DownPaymentStatus == DownPaymentStatus.Expired),
                It.IsAny<CancellationToken>()), Times.Once);

            _azureNotificationSenderServiceMock.Verify(x => x.Send(
                It.IsAny<SystemNotificationDto>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }
    }
}