﻿
using BlueTape.Services.LMS.Domain.Enums;
using System;
using System.Collections.Generic;

namespace BlueTape.Services.LMS.Application.Models.Loans
{
    public class AutoPayLoan
    {
        public Guid Id { get; set; }

        public decimal NextPaymentAmount { get; set; }

        public decimal OverDueAmount { get; set; }

        public DateOnly NextPaymentDate { get; set; }

        public bool IsOverdue { get; set; }

        public List<NextPaymentDetails>? NextPaymentDetails { get; set; }

        public LoanDueStatus DueStatus { get; set; }
    }
}
