﻿using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.Services.LMS.DataAccess.Company.Abstractions.Services;
using BlueTape.Services.LMS.DataAccess.Company.Extensions;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using VariableNullException = BlueTape.Common.ExceptionHandling.Exceptions.VariableNullException;

namespace BlueTape.Services.LMS.DataAccess.Company.Services;

public class CompanyService(ICompanyHttpClient companyHttpClient, ILogger<CompanyService> logger) : ICompanyService
{
    public Task<List<string>?> GetApplicableCompaniesIds(CancellationToken ct, AccountStatusEnum[]? statuses = null,
        AccountStatusEnum[]? statusesForExclude = null)
    {
        logger.LogInformation("{Method} started", nameof(GetApplicableCompaniesIds));
        List<AccountStatusEnum> statusesToSend = [];
        if (statusesForExclude is not null)
        {
            statusesToSend = Enum.GetValues<AccountStatusEnum>().Where(status => !statusesForExclude.Contains(status))
                .ToList();
        }

        if (statuses is null && statusesToSend.Count == 0)
            throw new VariableNullException("Empty statuses input params when we trying to get companies");
        if (statuses is not null) statusesToSend.AddRange(statuses);
        
        var result = companyHttpClient.GetCompanyByStatusesOnlyIds([.. statusesToSend], ct);
        
        logger.LogInformation("{Method} succeeded.", nameof(GetApplicableCompaniesIds));
        return result;
    }

    public async Task ThrowIfCompanyHasBadStandingStatus(string companyId, CancellationToken ct)
    {
        logger.LogInformation("{Method} started. companyId: {companyId}", nameof(ThrowIfCompanyHasBadStandingStatus), companyId);
        var company = await companyHttpClient.GetCompanyByIdAsync(companyId, ct);
        if (company is null)
            throw new VariableNullException($"Company with with id {companyId} not found");

        if (!company.IsCompanyHasGoodAccountStatus())
            throw new DeclineDrawException(
                $"Company with with id: {companyId} has invalid AccountStatus: {company.AccountStatus}");
        
        logger.LogInformation("{Method} succeeded.", nameof(ThrowIfCompanyHasBadStandingStatus));
    }
    
    public async Task<bool> CheckSendFinalPaymentWhenLoanIsPaid(string companyId, string customerAccountId, CancellationToken ct)
    {
        var company = await companyHttpClient.GetCompanyByIdAsync(companyId, ct);
        if (company?.Settings.SendFinalPaymentWhenLoanIsPaid is not true)
            return false;

        var customer = await companyHttpClient.GetCustomerByIdAsync(customerAccountId, ct);
        return customer?.Settings?.SendFinalPaymentWhenLoanIsPaid is true;
    }
}