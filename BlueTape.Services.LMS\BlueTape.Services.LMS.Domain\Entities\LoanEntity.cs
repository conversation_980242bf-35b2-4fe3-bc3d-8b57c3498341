﻿using BlueTape.OBS.Enums;
using BlueTape.Services.LMS.Domain.Constants;
using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.LMS.Domain.Entities
{
    public class LoanEntity : EntityWithId
    {
        public string? CompanyId { get; set; }
        public string? CompanyName { get; set; }
        public string? EinHash { get; set; }
        public string? ProjectId { get; set; }
        private decimal _amount;
        public decimal Amount
        {
            get => _amount;
            set => _amount = Math.Round(value, DigitConstants.NumberOfDigitsAfterPoint);
        }
        public decimal RefundAmount { get; set; }
        private decimal _fee;
        public decimal Fee
        {
            get => _fee;
            set => _fee = Math.Round(value, DigitConstants.NumberOfDigitsAfterPoint);
        }

        public Guid? ActiveLoanTemplateId { get; set; }
        public LoanTemplateEntity? ActiveLoanTemplate { get; set; }

        public Guid? PaymentProcessTemplateId { get; set; }
        public PaymentProcessTemplateEntity? PaymentProcessTemplate { get; set; }

        public List<LoanReceivableEntity> LoanReceivables { get; set; } = [];

        public List<LoanParametersEntity> LoanParameters { get; set; } = [];

        public List<PaymentEntity> Payments { get; set; } = [];
        public Guid? CreditId { get; set; }
        public CreditEntity? Credit { get; set; }
        public LoanStatus Status { get; set; }
        public LoanOrigin? LoanOrigin { get; set; }

        public bool IsDeleted { get; set; }
        public bool IsOverdue { get; set; }
        public DateOnly StartDate { get; set; } = default;
        public DateOnly? CloseDate { get; set; } = null!;
        public DateOnly? DefaultedDate { get; set; } = null!;
        public DateOnly? LastPaymentDate { get; set; }
        public DateTime? LastSyncDate { get; set; }
        public string? DrawApprovalId { get; set; }
        public List<LoanPayablesEntity> LoanPayables { get; set; } = [];
        public List<AuthorizationPeriodEntity> AuthorizationPeriods { get; set; } = [];
        public string? MerchantId { get; set; }
        public string? MerchantName { get; set; }
        public FundingSource FundingSource { get; set; }
        public bool SkipLateFeeGeneration { get; set; }
        public bool SkipPenaltyGeneration { get; set; }
        public DownPaymentStatus DownPaymentStatus { get; set; }
        public DateTime? DownPaymentStatusAt { get; set; }
        public bool IsAutoCollectionPaused { get; set; } = false;
        public DateTime? AutoCollectionPausedAt { get; set; }
        public string? AutoCollectionPausedBy { get; set; }
    }
}
