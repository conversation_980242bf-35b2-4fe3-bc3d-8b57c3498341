﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.Notification.Sender.Abstractions;
using BlueTape.Notification.Sender.Enums;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.OBS.Client.Abstractions;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.SNS.SlackNotification.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace BlueTape.Services.LMS.Application.Services.LoanServices;

public class LoanDownPaymentsService(
    ILoanRepository loanRepository,
    IAzureNotificationSenderService azureNotificationSenderService,
    IUserRoleRepository userRoleRepository,
    IUserRepository userRepository,
    ILogger<LoanDownPaymentsService> logger,
    ISlackNotificationService slackNotificationService,
    IOnBoardingIntegrationExternalService onboardingService,
    ITraceIdAccessor traceIdAccessor) : ILoanDownPaymentsService
{
    private const string DownPaymentReminderNotificationName = "DownPaymentReminder";
    private const string DownPaymentReminderSubject = "Action Required: Down Payment Reminder";
    private const string DownPaymentReminderTemplateId = "d-de96da19f81a498d9a90d806b1e10930";
    private const string EmailSender = "<EMAIL>";
    private const string EmailSenderName = "BlueTape Inc";

    private const string BucketName = "uw1.linqpal-user-assets";
    private const string Key = "instructions/down_payment_instructions.pdf";
    private const string FileName = "BlueTape ACH and Wiring Instructions.pdf";
    private const string ContentType = "application/pdf";

    public async Task CheckDownPaymentExpiration(CancellationToken ct)
    {
        logger.LogInformation("Starting check for expired down payments");

        try
        {
            // Get loans with "Due" down payment status
            var dueLoans = await loanRepository.GetByDownPaymentStatuses(new[] { DownPaymentStatus.Due.ToString() }, ct);

            if (dueLoans == null || !dueLoans.Any())
            {
                logger.LogInformation("No loans with due down payments found");
                return;
            }

            logger.LogInformation("Found {count} loans with due down payments", dueLoans.Count());

            var currentDate = DateTime.UtcNow;
            var loansToUpdate = new List<LoanEntity>();
            var loansToNotify = new List<LoanEntity>();

            foreach (var loan in dueLoans)
            {
                var activeLoanParams = loan.LoanParameters.FirstOrDefault(p => p.IsActive);

                // Check for expiration
                if (activeLoanParams != null &&
                    activeLoanParams.DownPaymentExpireAt.HasValue &&
                    activeLoanParams.DownPaymentExpireAt.Value < currentDate)
                {
                    loan.DownPaymentStatus = DownPaymentStatus.Expired;
                    loan.DownPaymentStatusAt = currentDate;
                    loansToUpdate.Add(loan);

                    logger.LogInformation("Down payment for loan {loanId} has expired. Expiration date: {expireDate}",
                        loan.Id, activeLoanParams.DownPaymentExpireAt.Value);
                }

                // Check if we need to send notification (2 days after creation date)
                var daysAfterApprovalAndCreation = (currentDate - loan.CreatedAt).TotalDays;
                if (daysAfterApprovalAndCreation is >= 2 and <= 3 && loan.Status == LoanStatus.Created) // Ensure we only notify once by checking last status update
                {
                    loansToNotify.Add(loan);

                    // Update status timestamp to mark that we've processed this loan
                    loan.DownPaymentStatusAt = currentDate;
                    if (!loansToUpdate.Contains(loan))
                    {
                        loansToUpdate.Add(loan);
                    }

                    logger.LogInformation("Marking loan {loanId} for down payment notification (created on {createdDate})",
                        loan.Id, loan.CreatedAt);
                }
            }

            // Update loans with expired down payments
            if (loansToUpdate.Any())
            {
                logger.LogInformation("Updating {count} loans with expired down payments or notification timestamps", loansToUpdate.Count);
                await loanRepository.UpdateRangeWithPayments(loansToUpdate, ct);
                logger.LogInformation("Successfully updated loans");
            }
            else
            {
                logger.LogInformation("No loans to update for down payment expiration or notification");
            }

            // Send notifications
            if (loansToNotify.Any())
            {
                logger.LogInformation("Sending notifications for {count} loans about down payment status", loansToNotify.Count);
                var invoices = loansToNotify.SelectMany(x => x.LoanPayables.Select(p => p.PayableId).ToList()).ToArray();
                var drawApprovals = await onboardingService.GetDrawApprovalsByInvoiceIds(
                    invoices,
                    ct);

                if (drawApprovals != null && !drawApprovals.Any())
                {
                    logger.LogError("No draw approval was found for loans to notify");
                    return;
                }


                foreach (var loan in loansToNotify)
                {
                    var drawApproval = drawApprovals?.FirstOrDefault(x => x.Id == loan.DrawApprovalId);

                    // Not sent notification if CompanyId is missing or payment method is not wire
                    if (string.IsNullOrEmpty(loan.CompanyId) ||
                        drawApproval == null ||
                        !string.Equals(drawApproval.DownPaymentDetails.PaymentMethod, "wire", StringComparison.OrdinalIgnoreCase))
                    {
                        logger.LogWarning("Cannot send notification for loan {loanId} because CompanyId is missing", loan.Id);
                        continue;
                    }

                    try
                    {
                        await SendDownPaymentExpirationNotification(loan, ct);
                        logger.LogInformation("Sent down payment notification for loan {loanId}", loan.Id);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Failed to send notification for loan {loanId}, but continuing with others", loan.Id);
                    }
                }
            }
            else
            {
                logger.LogInformation("No loans requiring down payment notifications found");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error checking down payment expiration");

            // Send notification to Slack for monitoring
            var message = new EventMessageBody
            {
                Message = $"Error in CheckDownPaymentExpiration: {ex.Message}\n{ex.StackTrace}",
                EventLevel = EventLevel.Error,
                EventName = "DownPaymentExpirationCheck",
                EventSource = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                ServiceName = "LMS",
                TimeStamp = DateTime.UtcNow.ToString()
            };

            try
            {
                await slackNotificationService.Notify(
                    message,
                    traceIdAccessor.TraceId,
                    CancellationToken.None);
            }
            catch (Exception notifyEx)
            {
                logger.LogError(notifyEx, "Failed to send Slack notification about down payment expiration check error");
            }

            throw;
        }
    }

    private async Task SendDownPaymentExpirationNotification(LoanEntity? loan, CancellationToken ct)
    {
        try
        {
            logger.LogInformation("Sending down payment expiration notification for loan {LoanId}, company {CompanyId}",
                loan.Id, loan.CompanyId);

            var emailReceivers = await GetCompanyNotificationReceivers(loan.CompanyId, ct);
            var firstOwner = emailReceivers.MaxBy(x => x.CreatedAt);

            var notificationsReferenceIds = loan.LoanPayables.Select(x => x.PayableId).ToList();
            notificationsReferenceIds.Add(loan.Id.ToString());
            var downPaymentIsExpiredNotification = GetNotification(
                DownPaymentReminderNotificationName,
                $"Action Required: Down Payment Reminder for loan: {loan.Id}",
                notificationsReferenceIds,
                loan.CompanyId);

            var dynamicEmailData = new
            {
                customerName = firstOwner?.FirstName ?? emailReceivers.FirstOrDefault(x => string.IsNullOrEmpty(x.FirstName))?.FirstName,
                dueDate = loan.CreatedAt.ToShortDateString(),
            };

            var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);

            downPaymentIsExpiredNotification.EmailDelivery = [new NotificationChannelDto<EmailPayloadDto>()
            {
                Payload = new EmailPayloadDto
                {
                    Html = string.Empty,
                    TemplateId = DownPaymentReminderTemplateId,
                    TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                    Subject = DownPaymentReminderSubject,
                    From = new EmailReceiverDataDto()
                    {
                        Email = EmailSender,
                        Name = EmailSenderName
                    },
                    Receivers = emailReceivers.Select(x => new EmailReceiverDataDto()
                    {
                        Email = x.Email,
                        Name = string.Empty
                    }).ToList(),
                    S3AttachmentReferences = new List<S3AttachmentReferenceDto>()
                    {
                        new()
                        {
                            BucketName = $"{env}.{BucketName}",
                            Key = Key,
                            FileName = FileName,
                            ContentType = ContentType
                        }
                    }
                }
            }];

            await azureNotificationSenderService.Send(downPaymentIsExpiredNotification, ct);

            logger.LogInformation("Successfully sent down payment expiration notification for loan {LoanId}, company {CompanyId}",
                loan.Id, loan.CompanyId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending down payment expiration notification for loan {LoanId}, company {CompanyId}",
                loan.Id, loan.CompanyId);
            throw;
        }
    }

    private SystemNotificationDto GetNotification(
        string notificationName, string notificationDescription, List<string> referenceIds, string? notificationReceiverCompanyId)
    {
        var systemNotification = new SystemNotificationDto
        {
            Source = NotificationSource.LMS,
            TraceId = traceIdAccessor.TraceId,
            CompanyId = notificationReceiverCompanyId,
            NotificationName = notificationName,
            Description = notificationDescription,
            ReferenceIds = referenceIds,
            EmailDelivery = new List<NotificationChannelDto<EmailPayloadDto>>(),
            UserUiReviewDelivery = new List<NotificationChannelDto<UserUiReviewPayloadDto>>(),
            BlueTapeBackOfficeDelivery = new List<NotificationChannelDto<BlueTapeBackOfficePayloadDto>>()
        };

        return systemNotification;
    }

    private async Task<List<UserDto>> GetCompanyNotificationReceivers(string? companyId, CancellationToken ct)
    {
        var receiversEmails = new List<UserDto>();

        if (companyId is not null)
        {
            logger.LogInformation("Getting subscribed emails");

            var activeOwners = userRoleRepository
                .GetByPredicate(ur => ur.CompanyId == companyId && ur.Status == "Active"
                                                                && ((ur.Settings != null &&
                                                                     ur.Settings.PaymentEmailNotificationsEnabled) ||
                                                                    ur.Settings == null), ct);

            foreach (var owner in activeOwners)
            {
                var user = (await userRepository.GetBySub(owner.Sub, ct)).FirstOrDefault();
                if (user is not null && !string.IsNullOrEmpty(user.Email))
                {
                    receiversEmails.Add(user);
                }
            }
        }

        return receiversEmails.Distinct().ToList();
    }
}
